# Deployment Guide - YouTube to MP3 Converter

## 🚀 Vercel Deployment (Recommended)

### Prerequisites
- GitHub/GitLab/Bitbucket repository
- Vercel account (free tier available)

### Step 1: Prepare Repository
1. Push your code to a Git repository
2. Ensure all files are committed and pushed

### Step 2: Deploy to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Sign in with your Git provider
3. Click "New Project"
4. Import your repository
5. Configure project settings:
   - **Framework Preset**: Next.js
   - **Root Directory**: `web`
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`

### Step 3: Environment Variables
Add the following environment variables in Vercel dashboard:

```bash
# Required for production
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NEXT_PUBLIC_API_URL=https://your-domain.vercel.app/api

# Optional: Add when implementing backend
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
```

### Step 4: Deploy
1. Click "Deploy"
2. Wait for build to complete
3. Your app will be available at `https://your-project.vercel.app`

## 🔧 Manual Deployment

### Build for Production
```bash
cd web
npm run build
npm run start
```

### Static Export (if needed)
```bash
# Add to next.config.ts
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

npm run build
```

## 🌐 Custom Domain

### Vercel Custom Domain
1. Go to Project Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed
4. SSL certificate will be automatically provisioned

## 📊 Monitoring & Analytics

### Built-in Vercel Analytics
- Automatically enabled for all deployments
- View in Vercel dashboard

### Google Analytics (Optional)
Add to environment variables:
```bash
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
```

## 🔒 Security Considerations

### Environment Variables
- Never commit `.env` files to repository
- Use Vercel's environment variable system
- Separate development and production variables

### CORS Configuration
- Already configured in `vercel.json`
- Adjust origins for production use

## 🚀 Performance Optimization

### Vercel Edge Functions
- API routes automatically deployed as Edge Functions
- Global distribution for low latency

### Image Optimization
- Next.js Image component used throughout
- Automatic WebP conversion and lazy loading

### Caching
- Static assets cached at CDN level
- API responses can be cached with appropriate headers

## 🔄 CI/CD Pipeline

### Automatic Deployments
- Connected Git repository triggers automatic deployments
- Preview deployments for pull requests
- Production deployment on main branch

### Build Optimization
```bash
# Vercel automatically runs:
npm ci
npm run build
```

## 📱 Progressive Web App (Future)

To enable PWA features:
1. Add `next-pwa` package
2. Configure service worker
3. Add manifest.json
4. Update next.config.ts

## 🐛 Troubleshooting

### Common Issues

**Build Failures:**
- Check Node.js version compatibility
- Verify all dependencies are installed
- Review build logs in Vercel dashboard

**Runtime Errors:**
- Check environment variables
- Review function logs
- Verify API endpoints

**Performance Issues:**
- Enable Vercel Analytics
- Check Core Web Vitals
- Optimize images and assets

### Debug Mode
```bash
# Local debugging
npm run dev
# Check build locally
npm run build && npm run start
```

## 📈 Scaling Considerations

### Current Architecture
- Static frontend (Next.js)
- Serverless API routes
- Ready for global CDN distribution

### Future Scaling
- Database: Supabase (PostgreSQL)
- File Storage: AWS S3
- Conversion: AWS Lambda
- Monitoring: Vercel Analytics + Sentry

## 🔗 Useful Links

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Vercel CLI](https://vercel.com/cli)

## 📞 Support

For deployment issues:
1. Check Vercel build logs
2. Review Next.js documentation
3. Check GitHub issues
4. Contact support if needed
