module.exports = {
  env: {
    browser: true,
    es2021: true,
    webextensions: true,
    node: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  globals: {
    chrome: 'readonly',
    browser: 'readonly'
  },
  rules: {
    // Code quality
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'no-console': 'off', // Allow console for debugging
    'no-debugger': 'warn',
    
    // Style
    'indent': ['error', 2],
    'linebreak-style': ['error', 'unix'],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    
    // Best practices
    'eqeqeq': 'error',
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // Variables
    'no-undef': 'error',
    'no-unused-expressions': 'error',
    
    // ES6+
    'prefer-const': 'error',
    'no-var': 'error',
    'prefer-arrow-callback': 'error',
    'arrow-spacing': 'error',
    
    // Async/await
    'require-await': 'error',
    'no-async-promise-executor': 'error',
    
    // Objects and arrays
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    
    // Functions
    'space-before-function-paren': ['error', {
      'anonymous': 'never',
      'named': 'never',
      'asyncArrow': 'always'
    }],
    
    // Spacing
    'space-infix-ops': 'error',
    'space-before-blocks': 'error',
    'keyword-spacing': 'error'
  },
  overrides: [
    {
      files: ['background/*.js'],
      env: {
        serviceworker: true
      }
    },
    {
      files: ['content/*.js'],
      env: {
        browser: true
      }
    },
    {
      files: ['popup/*.js'],
      env: {
        browser: true
      }
    },
    {
      files: ['build/*.js'],
      env: {
        node: true,
        browser: false
      }
    }
  ]
};
