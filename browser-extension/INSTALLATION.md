# YouTube to MP3 Converter - Installation Guide

This guide will help you install and set up the YouTube to MP3 Converter browser extension.

## 🚀 Quick Start

### 1. Build the Extension

First, navigate to the browser extension directory and build the extension:

```bash
cd browser-extension
npm install
npm run build
```

This will create two versions:
- `dist/chrome/` - For Chrome and Edge browsers
- `dist/firefox/` - For Firefox browser

### 2. Install in Chrome/Edge

1. Open Chrome or Edge browser
2. Navigate to the extensions page:
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`
3. Enable "Developer mode" (toggle in top-right corner)
4. Click "Load unpacked"
5. Select the `dist/chrome/` folder
6. The extension should now appear in your toolbar

### 3. Install in Firefox

1. Open Firefox browser
2. Navigate to `about:debugging`
3. Click "This Firefox" in the left sidebar
4. Click "Load Temporary Add-on..."
5. Navigate to `dist/firefox/` and select `manifest.json`
6. The extension will be loaded temporarily

## 🔧 Configuration

### API Endpoint Setup

Before using the extension, you need to configure the API endpoint:

1. Open the following files in a text editor:
   - `content/content.js` (line 7)
   - `popup/popup.js` (line 5)
   - `background/background.js` (line 5)

2. Replace `https://your-api-domain.com` with your actual API URL

3. Rebuild the extension:
   ```bash
   npm run build
   ```

4. Reload the extension in your browser

### Example API Configuration

```javascript
// Replace this line in all three files:
this.apiUrl = 'https://your-api-domain.com';

// With your actual API URL:
this.apiUrl = 'https://api.yourdomain.com';
```

## 📱 How to Use

### Method 1: Extension Popup

1. Click the extension icon in your browser toolbar
2. Paste a YouTube URL in the input field
3. Select your preferred bitrate and speed
4. Click "Convert to MP3"
5. Wait for the conversion to complete
6. Download will start automatically

### Method 2: YouTube Page Integration

1. Navigate to any YouTube video page
2. Look for the "Convert to MP3" button below the video
3. Click the button to open conversion options
4. Select bitrate and speed preferences
5. Click "Start Conversion"
6. Monitor progress in the popup
7. Download will start when complete

### Method 3: Context Menu

1. Right-click on any YouTube video link
2. Select "Convert to MP3" from the context menu
3. Follow the conversion process as above

## 🎯 Features Overview

### ✅ Supported YouTube URLs
- Regular videos: `https://www.youtube.com/watch?v=VIDEO_ID`
- YouTube Shorts: `https://www.youtube.com/shorts/VIDEO_ID`
- Short URLs: `https://youtu.be/VIDEO_ID`
- Embed URLs: `https://www.youtube.com/embed/VIDEO_ID`

### ✅ Conversion Options
- **Bitrate**: 128kbps, 192kbps, 320kbps
- **Speed**: 1.0x (Normal), 1.5x (Faster), 2.0x (Double)

### ✅ Additional Features
- Real-time conversion progress
- Conversion history tracking
- Settings persistence
- Error handling and notifications
- Dark mode support (follows system preference)

## 🛠️ Development Mode

For development and testing:

1. Make changes to the source files
2. Run the build command:
   ```bash
   npm run build
   ```
3. Reload the extension in your browser:
   - Chrome/Edge: Go to extensions page and click the reload icon
   - Firefox: Go to about:debugging and reload the temporary add-on

### Watch Mode

For continuous development:

```bash
npm run dev
```

This will watch for file changes and automatically rebuild the extension.

## 🐛 Troubleshooting

### Extension Not Loading

**Problem**: Extension fails to load or shows errors

**Solutions**:
- Check that all required files are present
- Verify manifest.json syntax
- Look for JavaScript errors in browser console
- Ensure you're using the correct build for your browser

### Button Not Appearing on YouTube

**Problem**: Convert button doesn't show on YouTube pages

**Solutions**:
- Refresh the YouTube page
- Check if extension has permission to access YouTube
- Verify content script is properly injected
- Look for errors in extension's background page console

### Conversion Fails

**Problem**: Conversion process fails or doesn't start

**Solutions**:
- Verify API endpoint is correctly configured
- Check network connectivity
- Ensure API server is running and accessible
- Look for error messages in extension popup

### Permission Issues

**Problem**: Extension requests additional permissions

**Solutions**:
- Review and accept required permissions
- Ensure extension has access to YouTube domains
- Check if browser is blocking extension permissions

## 📞 Getting Help

If you encounter issues:

1. Check the browser console for error messages
2. Review the extension's background page console
3. Verify all configuration steps were completed
4. Check the main project documentation
5. Open an issue on the project repository

## 🔄 Updates

To update the extension:

1. Pull the latest code changes
2. Rebuild the extension: `npm run build`
3. Reload the extension in your browser
4. Clear any cached data if needed

## 📋 Next Steps

After installation:

1. Test the extension with various YouTube URLs
2. Verify conversion options work correctly
3. Check that downloads complete successfully
4. Review conversion history functionality
5. Test on different YouTube page layouts

The extension is now ready to use! Enjoy converting your favorite YouTube videos to MP3 format.
