# YouTube to MP3 Converter - Browser Extension

A powerful browser extension that allows you to convert YouTube videos to MP3 directly from the YouTube page with just one click.

## 🚀 Features

- **One-Click Conversion**: Convert YouTube videos to MP3 directly from the video page
- **Multiple Quality Options**: Choose from 128kbps, 192kbps, or 320kbps
- **Speed Adjustment**: Convert at normal speed, 1.5x, or 2x speed
- **Real-time Progress**: See conversion progress with detailed status updates
- **Conversion History**: Keep track of your converted videos
- **Cross-Browser Support**: Works on Chrome, Edge, and Firefox
- **Beautiful UI**: Modern, responsive design with smooth animations
- **Context Menu Integration**: Right-click on YouTube links to convert

## 📦 Installation

### Chrome/Edge (Manifest V3)

1. Download the latest `chrome-extension.zip` from releases
2. Extract the zip file
3. Open Chrome/Edge and go to `chrome://extensions/` or `edge://extensions/`
4. Enable "Developer mode"
5. Click "Load unpacked" and select the extracted folder
6. The extension icon should appear in your toolbar

### Firefox (Manifest V2)

1. Download the latest `firefox-extension.zip` from releases
2. Open Firefox and go to `about:debugging`
3. Click "This Firefox"
4. Click "Load Temporary Add-on"
5. Select the `firefox-extension.zip` file
6. The extension will be loaded temporarily

## 🛠️ Development

### Prerequisites

- Node.js 14+ 
- npm or yarn

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd youtube_mp3/browser-extension
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the extension:
   ```bash
   npm run build
   ```

4. Load the extension in your browser:
   - Chrome/Edge: Load the `dist/chrome` folder as an unpacked extension
   - Firefox: Load the `dist/firefox` folder as a temporary add-on

### Development Scripts

- `npm run build` - Build extension for all browsers
- `npm run dev` - Build and watch for changes
- `npm run clean` - Clean build directory
- `npm run lint` - Run ESLint

### Project Structure

```
browser-extension/
├── manifest.json          # Extension manifest (Manifest V3)
├── content/               # Content scripts
│   ├── content.js        # Main content script
│   └── content.css       # Content script styles
├── popup/                 # Extension popup
│   ├── popup.html        # Popup HTML
│   ├── popup.css         # Popup styles
│   └── popup.js          # Popup logic
├── background/            # Background scripts
│   └── background.js     # Service worker
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── build/                 # Build scripts
│   └── build.js          # Main build script
└── dist/                  # Built extensions
    ├── chrome/           # Chrome/Edge build
    └── firefox/          # Firefox build
```

## 🎯 How It Works

1. **Content Script Injection**: When you visit a YouTube video page, the extension automatically injects a content script that adds a "Convert to MP3" button below the video.

2. **Conversion Options**: Click the button to open a popup with conversion options (bitrate, speed).

3. **Background Processing**: The extension communicates with your backend API to process the conversion.

4. **Download**: Once conversion is complete, the MP3 file is automatically downloaded.

5. **History Tracking**: All conversions are saved to your local history for easy access.

## 🔧 Configuration

### API Endpoint

Update the API URL in the following files:
- `content/content.js` - Line 7
- `popup/popup.js` - Line 5  
- `background/background.js` - Line 5

Replace `https://your-api-domain.com` with your actual API endpoint.

### Permissions

The extension requires the following permissions:
- `activeTab` - Access to the current tab
- `storage` - Local storage for settings and history
- `scripting` - Inject content scripts
- Host permissions for YouTube domains

## 🎨 Customization

### Styling

- Modify `content/content.css` to change the appearance of the conversion button
- Update `popup/popup.css` to customize the popup interface
- Colors and animations can be adjusted in the CSS files

### Features

- Add new conversion options in `popup/popup.html` and `popup/popup.js`
- Extend the content script in `content/content.js` for additional functionality
- Modify the background script in `background/background.js` for new API integrations

## 🐛 Troubleshooting

### Extension Not Loading
- Ensure you're using the correct manifest version for your browser
- Check the browser console for error messages
- Verify all required files are present

### Conversion Not Working
- Check that the API endpoint is correctly configured
- Verify network connectivity
- Look for errors in the extension's background page console

### Button Not Appearing
- Refresh the YouTube page
- Check if the content script is properly injected
- Ensure the extension has permission to access YouTube

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Open an issue on GitHub
- Check the troubleshooting section
- Review the browser console for error messages
