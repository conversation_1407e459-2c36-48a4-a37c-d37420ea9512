// YouTube to MP3 Converter - Background Script (Service Worker)

class BackgroundService {
  constructor() {
    this.apiUrl = 'https://your-api-domain.com'; // Replace with actual API URL
    this.init();
  }

  init() {
    // Listen for extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Listen for messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Listen for tab updates to inject content script if needed
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Context menu setup
    this.setupContextMenu();
  }

  handleInstallation(details) {
    console.log('YouTube to MP3 Converter installed:', details.reason);
    
    if (details.reason === 'install') {
      // First time installation
      this.setDefaultSettings();
      this.showWelcomeNotification();
    } else if (details.reason === 'update') {
      // Extension updated
      this.handleUpdate(details.previousVersion);
    }
  }

  async setDefaultSettings() {
    try {
      await chrome.storage.sync.set({
        defaultBitrate: '192',
        defaultSpeed: '1.0',
        autoDetectYouTube: true,
        showNotifications: true
      });
    } catch (error) {
      console.error('Failed to set default settings:', error);
    }
  }

  showWelcomeNotification() {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: '../icons/icon48.png',
      title: 'YouTube to MP3 Converter',
      message: 'Extension installed successfully! Visit any YouTube video to start converting.'
    });
  }

  handleUpdate(previousVersion) {
    console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
    // Handle any migration logic here if needed
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'CONVERT_VIDEO':
          const result = await this.convertVideo(message.data);
          sendResponse({ success: true, data: result });
          break;

        case 'GET_VIDEO_INFO':
          const videoInfo = await this.getVideoInfo(message.videoId);
          sendResponse({ success: true, data: videoInfo });
          break;

        case 'SAVE_CONVERSION':
          await this.saveConversion(message.data);
          sendResponse({ success: true });
          break;

        case 'GET_HISTORY':
          const history = await this.getConversionHistory();
          sendResponse({ success: true, data: history });
          break;

        case 'CLEAR_HISTORY':
          await this.clearHistory();
          sendResponse({ success: true });
          break;

        case 'SHOW_NOTIFICATION':
          this.showNotification(message.title, message.message, message.type);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    // Check if tab finished loading and is a YouTube page
    if (changeInfo.status === 'complete' && tab.url) {
      const isYouTubePage = /^https?:\/\/(www\.)?(youtube\.com\/(watch|shorts)|youtu\.be)/.test(tab.url);
      
      if (isYouTubePage) {
        // Ensure content script is injected
        this.ensureContentScriptInjected(tabId);
      }
    }
  }

  async ensureContentScriptInjected(tabId) {
    try {
      // Check if content script is already injected
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => window.youtubeMP3ConverterInjected
      });

      if (!results[0]?.result) {
        // Inject content script
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content/content.js']
        });

        await chrome.scripting.insertCSS({
          target: { tabId },
          files: ['content/content.css']
        });
      }
    } catch (error) {
      console.error('Failed to inject content script:', error);
    }
  }

  setupContextMenu() {
    chrome.contextMenus.create({
      id: 'convert-youtube-video',
      title: 'Convert to MP3',
      contexts: ['page', 'link'],
      targetUrlPatterns: [
        'https://www.youtube.com/watch*',
        'https://youtube.com/watch*',
        'https://www.youtube.com/shorts/*',
        'https://youtube.com/shorts/*',
        'https://youtu.be/*'
      ]
    });

    chrome.contextMenus.onClicked.addListener((info, tab) => {
      if (info.menuItemId === 'convert-youtube-video') {
        this.handleContextMenuClick(info, tab);
      }
    });
  }

  async handleContextMenuClick(info, tab) {
    try {
      const url = info.linkUrl || info.pageUrl;
      const videoId = this.extractVideoId(url);
      
      if (videoId) {
        // Send message to content script to show conversion popup
        await chrome.tabs.sendMessage(tab.id, {
          type: 'SHOW_CONVERSION_POPUP',
          videoId,
          url
        });
      }
    } catch (error) {
      console.error('Context menu click error:', error);
    }
  }

  extractVideoId(url) {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  async convertVideo(data) {
    const { videoId, bitrate, speed } = data;

    try {
      // Get the actual API URL from storage or use default
      const settings = await chrome.storage.sync.get(['apiUrl']);
      const apiUrl = settings.apiUrl || this.apiUrl;

      // Construct YouTube URL
      const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;

      // Make API call to conversion service
      const response = await fetch(`${apiUrl}/api/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          url: youtubeUrl,
          bitrate: parseInt(bitrate),
          speed: parseFloat(speed),
          source: 'browser-extension'
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Conversion failed: ${response.statusText}`);
      }

      const result = await response.json();

      // Save successful conversion to history
      await this.saveConversion({
        videoId,
        bitrate,
        speed,
        downloadUrl: result.downloadUrl,
        videoTitle: result.title,
        timestamp: Date.now(),
        status: 'completed'
      });

      // Trigger download if URL is provided
      if (result.downloadUrl) {
        await this.triggerDownload(result.downloadUrl, result.filename || `${videoId}.mp3`);
      }

      return result;
    } catch (error) {
      console.error('Conversion error:', error);

      // Save failed conversion to history
      await this.saveConversion({
        videoId,
        bitrate,
        speed,
        timestamp: Date.now(),
        status: 'failed',
        error: error.message
      });

      throw error;
    }
  }

  async triggerDownload(url, filename) {
    try {
      await chrome.downloads.download({
        url: url,
        filename: filename,
        saveAs: false
      });
    } catch (error) {
      console.error('Download trigger failed:', error);
      // Fallback: open URL in new tab
      await chrome.tabs.create({ url: url });
    }
  }

  async getVideoInfo(videoId) {
    try {
      const response = await fetch(`${this.apiUrl}/video-info/${videoId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to get video info: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get video info:', error);
      throw error;
    }
  }

  async saveConversion(data) {
    try {
      const result = await chrome.storage.local.get(['conversionHistory']);
      const history = result.conversionHistory || [];
      
      // Add new conversion to beginning of array
      history.unshift(data);
      
      // Keep only last 100 conversions
      const trimmedHistory = history.slice(0, 100);
      
      await chrome.storage.local.set({ conversionHistory: trimmedHistory });
    } catch (error) {
      console.error('Failed to save conversion:', error);
      throw error;
    }
  }

  async getConversionHistory() {
    try {
      const result = await chrome.storage.local.get(['conversionHistory']);
      return result.conversionHistory || [];
    } catch (error) {
      console.error('Failed to get conversion history:', error);
      throw error;
    }
  }

  async clearHistory() {
    try {
      await chrome.storage.local.remove(['conversionHistory']);
    } catch (error) {
      console.error('Failed to clear history:', error);
      throw error;
    }
  }

  showNotification(title, message, type = 'basic') {
    const iconUrl = type === 'error' ? '../icons/icon48.png' : '../icons/icon48.png';
    
    chrome.notifications.create({
      type: 'basic',
      iconUrl,
      title,
      message
    });
  }
}

// Initialize background service
new BackgroundService();
