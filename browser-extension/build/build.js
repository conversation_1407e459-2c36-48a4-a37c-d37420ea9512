#!/usr/bin/env node

// YouTube to MP3 Converter - Build Script
// This script packages the extension for different browsers

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class ExtensionBuilder {
  constructor() {
    this.rootDir = path.join(__dirname, '..');
    this.buildDir = path.join(this.rootDir, 'dist');
    this.manifestPath = path.join(this.rootDir, 'manifest.json');
  }

  async build() {
    console.log('🚀 Building YouTube to MP3 Converter Extension...\n');

    try {
      // Clean build directory
      await this.cleanBuildDir();

      // Create build directory
      await this.createBuildDir();

      // Generate icons if needed
      await this.generateIcons();

      // Build for Chrome/Edge (Manifest V3)
      await this.buildChrome();

      // Build for Firefox (Manifest V2 compatibility)
      await this.buildFirefox();

      console.log('✅ Build completed successfully!\n');
      console.log('📦 Output files:');
      console.log(`   - Chrome/Edge: ${path.join(this.buildDir, 'chrome-extension.zip')}`);
      console.log(`   - Firefox: ${path.join(this.buildDir, 'firefox-extension.zip')}`);
      console.log(`   - Test page: ${path.join(this.rootDir, 'test', 'test.html')}`);

    } catch (error) {
      console.error('❌ Build failed:', error.message);
      process.exit(1);
    }
  }

  async generateIcons() {
    console.log('🎨 Generating icons...');

    try {
      const IconGenerator = require('./generate-icons');
      const generator = new IconGenerator();
      await generator.generateIcons();
    } catch (error) {
      console.log('⚠️  Icon generation failed, using placeholders:', error.message);
      await this.createPlaceholderIcons();
    }
  }

  async createPlaceholderIcons() {
    const iconsDir = path.join(this.rootDir, 'icons');
    const sizes = [16, 32, 48, 128];

    // Create simple placeholder icons
    for (const size of sizes) {
      const iconPath = path.join(iconsDir, `icon${size}.png`);
      if (!fs.existsSync(iconPath)) {
        // Create a minimal PNG placeholder
        const placeholder = this.createMinimalIcon(size);
        fs.writeFileSync(iconPath, placeholder);
        console.log(`📝 Created placeholder icon${size}.png`);
      }
    }
  }

  createMinimalIcon(size) {
    // Create a very basic PNG file (simplified)
    // In production, use proper image libraries
    const buffer = Buffer.alloc(100);
    buffer.write('\x89PNG\r\n\x1a\n', 0, 'binary');
    return buffer;
  }

  async cleanBuildDir() {
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true });
    }
  }

  async createBuildDir() {
    fs.mkdirSync(this.buildDir, { recursive: true });
  }

  async buildChrome() {
    console.log('📦 Building Chrome/Edge extension...');
    
    const chromeDir = path.join(this.buildDir, 'chrome');
    fs.mkdirSync(chromeDir, { recursive: true });

    // Copy all files except build directory
    await this.copyFiles(this.rootDir, chromeDir, ['dist', 'build', 'node_modules', '.git']);

    // Create zip file
    await this.createZip(chromeDir, path.join(this.buildDir, 'chrome-extension.zip'));
    
    console.log('✅ Chrome/Edge extension built');
  }

  async buildFirefox() {
    console.log('📦 Building Firefox extension...');
    
    const firefoxDir = path.join(this.buildDir, 'firefox');
    fs.mkdirSync(firefoxDir, { recursive: true });

    // Copy all files
    await this.copyFiles(this.rootDir, firefoxDir, ['dist', 'build', 'node_modules', '.git']);

    // Modify manifest for Firefox compatibility
    await this.modifyManifestForFirefox(firefoxDir);

    // Create zip file
    await this.createZip(firefoxDir, path.join(this.buildDir, 'firefox-extension.zip'));
    
    console.log('✅ Firefox extension built');
  }

  async copyFiles(src, dest, exclude = []) {
    const items = fs.readdirSync(src);

    for (const item of items) {
      if (exclude.includes(item)) continue;

      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      const stat = fs.statSync(srcPath);

      if (stat.isDirectory()) {
        fs.mkdirSync(destPath, { recursive: true });
        await this.copyFiles(srcPath, destPath, exclude);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  async modifyManifestForFirefox(firefoxDir) {
    const manifestPath = path.join(firefoxDir, 'manifest.json');
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

    // Firefox-specific modifications
    manifest.manifest_version = 2;
    
    // Convert background service worker to background scripts
    if (manifest.background && manifest.background.service_worker) {
      manifest.background = {
        scripts: [manifest.background.service_worker],
        persistent: false
      };
    }

    // Convert action to browser_action for Manifest V2
    if (manifest.action) {
      manifest.browser_action = manifest.action;
      delete manifest.action;
    }

    // Add Firefox-specific fields
    manifest.applications = {
      gecko: {
        id: '<EMAIL>',
        strict_min_version: '57.0'
      }
    };

    // Update permissions for Manifest V2
    if (manifest.host_permissions) {
      manifest.permissions = [...(manifest.permissions || []), ...manifest.host_permissions];
      delete manifest.host_permissions;
    }

    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  }

  async createZip(sourceDir, outputPath) {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => resolve());
      archive.on('error', (err) => reject(err));

      archive.pipe(output);
      archive.directory(sourceDir, false);
      archive.finalize();
    });
  }
}

// Run build if called directly
if (require.main === module) {
  const builder = new ExtensionBuilder();
  builder.build();
}

module.exports = ExtensionBuilder;
