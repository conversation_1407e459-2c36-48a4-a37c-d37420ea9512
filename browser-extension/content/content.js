// YouTube to MP3 Converter - Content Script
// This script runs on YouTube pages and adds the conversion button

// Mark as injected to prevent duplicate injection
window.youtubeMP3ConverterInjected = true;

class YouTubeMP3Converter {
  constructor() {
    this.apiUrl = 'https://your-api-domain.com'; // Will be loaded from settings
    this.isInjected = false;
    this.currentVideoId = null;
    this.conversionButton = null;
    this.settings = {};
    this.init();
  }

  async init() {
    // Load settings first
    await this.loadSettings();

    // Only proceed if auto-injection is enabled
    if (!this.settings.autoInject) return;

    // Wait for page to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.injectButton());
    } else {
      this.injectButton();
    }

    // Listen for navigation changes (YouTube is SPA)
    this.observeUrlChanges();

    // Listen for settings updates
    this.setupMessageListener();
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get([
        'apiUrl', 'autoInject', 'defaultBitrate', 'defaultSpeed'
      ]);

      this.settings = {
        apiUrl: result.apiUrl || this.apiUrl,
        autoInject: result.autoInject !== false, // Default to true
        defaultBitrate: result.defaultBitrate || '192',
        defaultSpeed: result.defaultSpeed || '1.0'
      };

      this.apiUrl = this.settings.apiUrl;
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.settings = {
        apiUrl: this.apiUrl,
        autoInject: true,
        defaultBitrate: '192',
        defaultSpeed: '1.0'
      };
    }
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'SETTINGS_UPDATED') {
        this.settings = message.settings;
        this.apiUrl = this.settings.apiUrl;

        // Re-inject button if auto-inject setting changed
        if (this.settings.autoInject && !this.isInjected) {
          this.injectButton();
        } else if (!this.settings.autoInject && this.isInjected) {
          this.removeExistingButton();
          this.isInjected = false;
        }
      } else if (message.type === 'SHOW_CONVERSION_POPUP') {
        this.showConversionPopup(message.videoId, message.url);
      }

      sendResponse({ success: true });
    });
  }

  observeUrlChanges() {
    let lastUrl = location.href;
    new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        this.currentVideoId = this.extractVideoId(url);
        setTimeout(() => this.injectButton(), 1000); // Delay for page elements to load
      }
    }).observe(document, { subtree: true, childList: true });
  }

  extractVideoId(url) {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  injectButton() {
    // Only inject on video pages
    if (!this.isVideoPage()) return;

    // Remove existing button if present
    this.removeExistingButton();

    // Find the target container (below video player)
    const targetContainer = this.findTargetContainer();
    if (!targetContainer) {
      console.log('YouTube MP3: Target container not found, retrying...');
      setTimeout(() => this.injectButton(), 2000);
      return;
    }

    // Create and inject the button
    this.createConversionButton(targetContainer);
    this.isInjected = true;
  }

  isVideoPage() {
    return window.location.pathname === '/watch' || window.location.pathname.startsWith('/shorts/');
  }

  findTargetContainer() {
    // Try multiple selectors for different YouTube layouts
    const selectors = [
      '#actions', // New YouTube layout
      '#watch-header', // Older layout
      '#info', // Alternative
      '.ytd-video-primary-info-renderer', // Another option
      '#primary-inner' // Fallback
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) return element;
    }

    return null;
  }

  removeExistingButton() {
    const existingButton = document.querySelector('#yt-mp3-converter-btn');
    if (existingButton) {
      existingButton.remove();
    }
  }

  createConversionButton(container) {
    // Create button container
    const buttonContainer = document.createElement('div');
    buttonContainer.id = 'yt-mp3-converter-btn';
    buttonContainer.className = 'yt-mp3-converter-container';

    // Create the main button
    const button = document.createElement('button');
    button.className = 'yt-mp3-converter-button';
    button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
      </svg>
      Convert to MP3
    `;

    // Add click event
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.handleConversion();
    });

    buttonContainer.appendChild(button);
    
    // Insert button after the target container
    container.parentNode.insertBefore(buttonContainer, container.nextSibling);
    
    this.conversionButton = button;
  }

  async handleConversion() {
    const videoId = this.extractVideoId(window.location.href);
    if (!videoId) {
      this.showNotification('Error: Could not extract video ID', 'error');
      return;
    }

    // Get video title
    const videoTitle = this.getVideoTitle();
    
    // Show conversion options popup
    this.showConversionPopup(videoId, videoTitle);
  }

  getVideoTitle() {
    const titleSelectors = [
      'h1.ytd-video-primary-info-renderer',
      '.ytd-video-primary-info-renderer h1',
      '#container h1',
      'h1'
    ];

    for (const selector of titleSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        return element.textContent.trim();
      }
    }

    return 'Unknown Video';
  }

  showConversionPopup(videoId, videoTitle) {
    // Remove existing popup
    const existingPopup = document.querySelector('#yt-mp3-popup');
    if (existingPopup) existingPopup.remove();

    // Create popup overlay
    const overlay = document.createElement('div');
    overlay.id = 'yt-mp3-popup';
    overlay.className = 'yt-mp3-popup-overlay';

    // Create popup content
    const popup = document.createElement('div');
    popup.className = 'yt-mp3-popup';
    popup.innerHTML = `
      <div class="yt-mp3-popup-header">
        <h3>Convert to MP3</h3>
        <button class="yt-mp3-close-btn">&times;</button>
      </div>
      <div class="yt-mp3-popup-content">
        <div class="video-info">
          <strong>Video:</strong> ${videoTitle}
        </div>
        
        <div class="conversion-options">
          <div class="option-group">
            <label>Bitrate:</label>
            <select id="bitrate-select">
              <option value="128">128 kbps</option>
              <option value="192" selected>192 kbps</option>
              <option value="320">320 kbps</option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Speed:</label>
            <select id="speed-select">
              <option value="1.0" selected>1.0x (Normal)</option>
              <option value="1.5">1.5x (Faster)</option>
              <option value="2.0">2.0x (Double)</option>
            </select>
          </div>
        </div>
        
        <div class="conversion-progress" id="conversion-progress" style="display: none;">
          <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
          </div>
          <div class="progress-text" id="progress-text">Preparing conversion...</div>
        </div>
        
        <div class="popup-actions">
          <button class="yt-mp3-cancel-btn">Cancel</button>
          <button class="yt-mp3-convert-btn" id="start-conversion">Start Conversion</button>
        </div>
      </div>
    `;

    overlay.appendChild(popup);
    document.body.appendChild(overlay);

    // Add event listeners
    this.setupPopupEventListeners(overlay, videoId, videoTitle);
  }

  setupPopupEventListeners(overlay, videoId, videoTitle) {
    // Close button
    overlay.querySelector('.yt-mp3-close-btn').addEventListener('click', () => {
      overlay.remove();
    });

    // Cancel button
    overlay.querySelector('.yt-mp3-cancel-btn').addEventListener('click', () => {
      overlay.remove();
    });

    // Click outside to close
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        overlay.remove();
      }
    });

    // Start conversion button
    overlay.querySelector('#start-conversion').addEventListener('click', () => {
      const bitrate = overlay.querySelector('#bitrate-select').value;
      const speed = overlay.querySelector('#speed-select').value;
      this.startConversion(videoId, videoTitle, bitrate, speed, overlay);
    });
  }

  async startConversion(videoId, videoTitle, bitrate, speed, overlay) {
    const progressContainer = overlay.querySelector('#conversion-progress');
    const progressFill = overlay.querySelector('#progress-fill');
    const progressText = overlay.querySelector('#progress-text');
    const convertBtn = overlay.querySelector('#start-conversion');

    // Show progress and disable button
    progressContainer.style.display = 'block';
    convertBtn.disabled = true;
    convertBtn.textContent = 'Converting...';

    try {
      // Simulate conversion progress (replace with actual API calls)
      await this.simulateConversion(progressFill, progressText);
      
      // Show success
      progressText.textContent = 'Conversion completed! Download will start shortly.';
      progressFill.style.width = '100%';
      
      // Simulate download
      setTimeout(() => {
        this.showNotification('MP3 conversion completed!', 'success');
        overlay.remove();
      }, 2000);

    } catch (error) {
      console.error('Conversion error:', error);
      progressText.textContent = 'Conversion failed. Please try again.';
      convertBtn.disabled = false;
      convertBtn.textContent = 'Retry';
      this.showNotification('Conversion failed. Please try again.', 'error');
    }
  }

  async simulateConversion(progressFill, progressText) {
    const steps = [
      { progress: 20, text: 'Downloading video...' },
      { progress: 50, text: 'Converting to MP3...' },
      { progress: 80, text: 'Optimizing audio...' },
      { progress: 100, text: 'Preparing download...' }
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      progressFill.style.width = step.progress + '%';
      progressText.textContent = step.text;
    }
  }

  showNotification(message, type = 'info') {
    // Remove existing notification
    const existing = document.querySelector('.yt-mp3-notification');
    if (existing) existing.remove();

    // Create notification
    const notification = document.createElement('div');
    notification.className = `yt-mp3-notification yt-mp3-notification-${type}`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }
}

// Initialize the converter when the script loads
new YouTubeMP3Converter();
