// YouTube to MP3 Converter - Background Script (Service Worker)

class BackgroundService {
  constructor() {
    this.webAppUrl = 'http://localhost:3000'; // Default web app URL
    this.init();
  }

  init() {
    // Listen for extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Listen for messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Listen for tab updates to inject content script if needed
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Context menu setup
    this.setupContextMenu();
  }

  handleInstallation(details) {
    console.log('YouTube to MP3 Converter installed:', details.reason);
    
    if (details.reason === 'install') {
      // First time installation
      this.setDefaultSettings();
      this.showWelcomeNotification();
    } else if (details.reason === 'update') {
      // Extension updated
      this.handleUpdate(details.previousVersion);
    }
  }

  async setDefaultSettings() {
    try {
      await chrome.storage.sync.set({
        webAppUrl: 'http://localhost:3000',
        defaultBitrate: '192',
        defaultSpeed: '1.0',
        autoInject: true,
        showNotifications: true,
        openInNewTab: true
      });
    } catch (error) {
      console.error('Failed to set default settings:', error);
    }
  }

  showWelcomeNotification() {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: '../icons/icon48.png',
      title: 'YouTube to MP3 Converter',
      message: 'Extension installed successfully! Visit any YouTube video to start converting.'
    });
  }

  handleUpdate(previousVersion) {
    console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
    // Handle any migration logic here if needed
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'REDIRECT_TO_WEB_APP':
          await this.redirectToWebApp(message.data);
          sendResponse({ success: true });
          break;

        case 'SAVE_CONVERSION':
          await this.saveConversion(message.data);
          sendResponse({ success: true });
          break;

        case 'GET_HISTORY':
          const history = await this.getConversionHistory();
          sendResponse({ success: true, data: history });
          break;

        case 'CLEAR_HISTORY':
          await this.clearHistory();
          sendResponse({ success: true });
          break;

        case 'SHOW_NOTIFICATION':
          this.showNotification(message.title, message.message, message.type);
          sendResponse({ success: true });
          break;

        case 'PING':
          sendResponse({ success: true, message: 'Extension is working' });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    // Check if tab finished loading and is a YouTube page
    if (changeInfo.status === 'complete' && tab.url) {
      const isYouTubePage = /^https?:\/\/(www\.)?(youtube\.com\/(watch|shorts)|youtu\.be)/.test(tab.url);
      
      if (isYouTubePage) {
        // Ensure content script is injected
        this.ensureContentScriptInjected(tabId);
      }
    }
  }

  async ensureContentScriptInjected(tabId) {
    try {
      // Check if content script is already injected
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => window.youtubeMP3ConverterInjected
      });

      if (!results[0]?.result) {
        // Inject content script
        await chrome.scripting.executeScript({
          target: { tabId },
          files: ['content/content.js']
        });

        await chrome.scripting.insertCSS({
          target: { tabId },
          files: ['content/content.css']
        });
      }
    } catch (error) {
      console.error('Failed to inject content script:', error);
    }
  }

  setupContextMenu() {
    chrome.contextMenus.create({
      id: 'convert-youtube-video',
      title: 'Convert to MP3',
      contexts: ['page', 'link'],
      targetUrlPatterns: [
        'https://www.youtube.com/watch*',
        'https://youtube.com/watch*',
        'https://www.youtube.com/shorts/*',
        'https://youtube.com/shorts/*',
        'https://youtu.be/*'
      ]
    });

    chrome.contextMenus.onClicked.addListener((info, tab) => {
      if (info.menuItemId === 'convert-youtube-video') {
        this.handleContextMenuClick(info, tab);
      }
    });
  }

  async handleContextMenuClick(info, tab) {
    try {
      const url = info.linkUrl || info.pageUrl;
      const videoId = this.extractVideoId(url);
      
      if (videoId) {
        // Send message to content script to show conversion popup
        await chrome.tabs.sendMessage(tab.id, {
          type: 'SHOW_CONVERSION_POPUP',
          videoId,
          url
        });
      }
    } catch (error) {
      console.error('Context menu click error:', error);
    }
  }

  extractVideoId(url) {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  async redirectToWebApp(data) {
    const { videoId, bitrate, speed, videoTitle } = data;

    try {
      // Get web app URL from settings
      const settings = await chrome.storage.sync.get(['webAppUrl', 'openInNewTab']);
      const webAppUrl = settings.webAppUrl || this.webAppUrl;
      const openInNewTab = settings.openInNewTab !== false; // Default to true

      // Construct YouTube URL
      const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;

      // Prepare parameters for web app
      const params = new URLSearchParams({
        url: youtubeUrl,
        bitrate: bitrate,
        speed: speed,
        source: 'browser-extension'
      });

      if (videoTitle) {
        params.append('title', videoTitle);
      }

      // Construct the redirect URL
      const redirectUrl = `${webAppUrl}?${params.toString()}`;

      // Open web app
      if (openInNewTab) {
        await chrome.tabs.create({ url: redirectUrl });
      } else {
        // Update current tab
        const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await chrome.tabs.update(currentTab.id, { url: redirectUrl });
      }

      // Save to history
      await this.saveConversion({
        videoId,
        bitrate,
        speed,
        videoTitle,
        timestamp: Date.now(),
        status: 'redirected',
        webAppUrl: redirectUrl
      });

      return { success: true, redirectUrl };
    } catch (error) {
      console.error('Redirect error:', error);
      throw error;
    }
  }



  async saveConversion(data) {
    try {
      const result = await chrome.storage.local.get(['conversionHistory']);
      const history = result.conversionHistory || [];
      
      // Add new conversion to beginning of array
      history.unshift(data);
      
      // Keep only last 100 conversions
      const trimmedHistory = history.slice(0, 100);
      
      await chrome.storage.local.set({ conversionHistory: trimmedHistory });
    } catch (error) {
      console.error('Failed to save conversion:', error);
      throw error;
    }
  }

  async getConversionHistory() {
    try {
      const result = await chrome.storage.local.get(['conversionHistory']);
      return result.conversionHistory || [];
    } catch (error) {
      console.error('Failed to get conversion history:', error);
      throw error;
    }
  }

  async clearHistory() {
    try {
      await chrome.storage.local.remove(['conversionHistory']);
    } catch (error) {
      console.error('Failed to clear history:', error);
      throw error;
    }
  }

  showNotification(title, message, type = 'basic') {
    const iconUrl = type === 'error' ? '../icons/icon48.png' : '../icons/icon48.png';
    
    chrome.notifications.create({
      type: 'basic',
      iconUrl,
      title,
      message
    });
  }
}

// Initialize background service
new BackgroundService();
