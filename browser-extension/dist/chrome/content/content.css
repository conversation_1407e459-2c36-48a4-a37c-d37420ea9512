/* YouTube to MP3 Converter - Content Script Styles */

.yt-mp3-converter-container {
  margin: 12px 0;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.yt-mp3-converter-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 300%;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: rainbow-border 3s ease-in-out infinite;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.yt-mp3-converter-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation-duration: 1.5s;
}

.yt-mp3-converter-button:active {
  transform: translateY(0);
}

.yt-mp3-converter-button svg {
  width: 20px;
  height: 20px;
}

@keyframes rainbow-border {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Popup Overlay */
.yt-mp3-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

/* Popup Container */
.yt-mp3-popup {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 480px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: popup-appear 0.3s ease-out;
}

@keyframes popup-appear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Popup Header */
.yt-mp3-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.yt-mp3-popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.yt-mp3-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.yt-mp3-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Popup Content */
.yt-mp3-popup-content {
  padding: 24px;
}

.video-info {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  border-left: 4px solid #667eea;
}

.conversion-options {
  margin-bottom: 20px;
}

.option-group {
  margin-bottom: 16px;
}

.option-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.option-group select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
}

.option-group select:focus {
  outline: none;
  border-color: #667eea;
}

/* Progress Bar */
.conversion-progress {
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* Popup Actions */
.popup-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.yt-mp3-cancel-btn,
.yt-mp3-convert-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.yt-mp3-cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.yt-mp3-cancel-btn:hover {
  background: #e9ecef;
}

.yt-mp3-convert-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.yt-mp3-convert-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.yt-mp3-convert-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Notification */
.yt-mp3-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  z-index: 10001;
  animation: notification-slide 0.3s ease-out;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.yt-mp3-notification-success {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.yt-mp3-notification-error {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.yt-mp3-notification-info {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

@keyframes notification-slide {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Dark mode support for YouTube */
[dark] .yt-mp3-popup {
  background: #212121;
  color: white;
}

[dark] .video-info {
  background: #333;
  color: white;
}

[dark] .option-group select {
  background: #333;
  color: white;
  border-color: #555;
}

[dark] .progress-bar {
  background: #555;
}

[dark] .yt-mp3-cancel-btn {
  background: #333;
  color: white;
  border-color: #555;
}

[dark] .yt-mp3-cancel-btn:hover {
  background: #444;
}
