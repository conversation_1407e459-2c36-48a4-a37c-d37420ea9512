<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube to MP3 Converter - Settings</title>
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <header class="header">
      <div class="logo">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
        </svg>
        <h1>YouTube to MP3 Converter</h1>
      </div>
      <div class="version">v1.0.0</div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Web App Configuration -->
      <section class="settings-section">
        <h2>Web Application Configuration</h2>
        <div class="setting-group">
          <label for="web-app-url">Web App URL:</label>
          <input
            type="url"
            id="web-app-url"
            placeholder="http://localhost:3000"
            class="input-field"
          >
          <p class="help-text">
            Enter the URL of your YouTube to MP3 web application.
            The extension will redirect to this URL for conversion.
          </p>
        </div>

        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="open-in-new-tab" checked>
            <span class="checkmark"></span>
            Open web app in new tab
          </label>
          <p class="help-text">
            When enabled, the web app will open in a new browser tab instead of the current tab.
          </p>
        </div>
      </section>

      <!-- Default Settings -->
      <section class="settings-section">
        <h2>Default Conversion Settings</h2>
        <div class="setting-group">
          <label for="default-bitrate">Default Audio Quality:</label>
          <select id="default-bitrate" class="select-field">
            <option value="128">128 kbps (Good)</option>
            <option value="192" selected>192 kbps (Better)</option>
            <option value="320">320 kbps (Best)</option>
          </select>
        </div>

        <div class="setting-group">
          <label for="default-speed">Default Playback Speed:</label>
          <select id="default-speed" class="select-field">
            <option value="1.0" selected>1.0x (Normal)</option>
            <option value="1.5">1.5x (Faster)</option>
            <option value="2.0">2.0x (Double)</option>
          </select>
        </div>
      </section>

      <!-- Behavior Settings -->
      <section class="settings-section">
        <h2>Extension Behavior</h2>
        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="auto-inject" checked>
            <span class="checkmark"></span>
            Automatically show convert button on YouTube pages
          </label>
        </div>

        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="show-notifications" checked>
            <span class="checkmark"></span>
            Show desktop notifications for conversion status
          </label>
        </div>

        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="save-history" checked>
            <span class="checkmark"></span>
            Save conversion history locally
          </label>
        </div>

        <div class="setting-group">
          <label class="checkbox-label">
            <input type="checkbox" id="auto-download">
            <span class="checkmark"></span>
            Automatically start download when conversion completes
          </label>
        </div>
      </section>

      <!-- Privacy Settings -->
      <section class="settings-section">
        <h2>Privacy & Data</h2>
        <div class="setting-group">
          <label for="history-retention">Keep conversion history for:</label>
          <select id="history-retention" class="select-field">
            <option value="7">7 days</option>
            <option value="30" selected>30 days</option>
            <option value="90">90 days</option>
            <option value="365">1 year</option>
            <option value="0">Forever</option>
          </select>
        </div>

        <div class="setting-group">
          <button id="clear-history" class="secondary-button">
            Clear All History
          </button>
          <p class="help-text">
            This will permanently delete all stored conversion history.
          </p>
        </div>
      </section>

      <!-- Actions -->
      <section class="actions-section">
        <button id="save-settings" class="primary-button">
          Save Settings
        </button>
        <button id="reset-settings" class="secondary-button">
          Reset to Defaults
        </button>
        <button id="test-connection" class="secondary-button">
          Test Web App Connection
        </button>
      </section>

      <!-- Status Messages -->
      <div id="status-message" class="status-message" style="display: none;"></div>
    </main>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-links">
        <a href="#" id="help-link">Help & Documentation</a>
        <a href="#" id="github-link">GitHub Repository</a>
        <a href="#" id="report-issue">Report Issue</a>
      </div>
      <div class="footer-text">
        <p>&copy; 2024 YouTube to MP3 Converter. Open source project.</p>
      </div>
    </footer>
  </div>

  <script src="options.js"></script>
</body>
</html>
