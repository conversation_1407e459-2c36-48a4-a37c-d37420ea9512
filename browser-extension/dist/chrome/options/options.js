// YouTube to MP3 Converter - Options Page Script

class OptionsController {
  constructor() {
    this.defaultSettings = {
      webAppUrl: 'http://localhost:3000',
      openInNewTab: true,
      defaultBitrate: '192',
      defaultSpeed: '1.0',
      autoInject: true,
      showNotifications: true,
      saveHistory: true,
      historyRetention: 30
    };

    this.init();
  }

  async init() {
    await this.loadSettings();
    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    // Save settings button
    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    // Reset settings button
    document.getElementById('reset-settings').addEventListener('click', () => {
      this.resetSettings();
    });

    // Test connection button
    document.getElementById('test-connection').addEventListener('click', () => {
      this.testConnection();
    });

    // Clear history button
    document.getElementById('clear-history').addEventListener('click', () => {
      this.clearHistory();
    });

    // Footer links
    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/youtube-mp3-converter#readme' });
    });

    document.getElementById('github-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/youtube-mp3-converter' });
    });

    document.getElementById('report-issue').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/youtube-mp3-converter/issues' });
    });

    // Auto-save on input changes
    this.setupAutoSave();
  }

  setupAutoSave() {
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
      input.addEventListener('change', () => {
        this.saveSettings(false); // Save without showing message
      });
    });
  }

  async loadSettings() {
    try {
      const stored = await chrome.storage.sync.get(Object.keys(this.defaultSettings));
      this.settings = { ...this.defaultSettings, ...stored };
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.settings = { ...this.defaultSettings };
    }
  }

  updateUI() {
    // Update form fields with current settings
    document.getElementById('web-app-url').value = this.settings.webAppUrl;
    document.getElementById('open-in-new-tab').checked = this.settings.openInNewTab;
    document.getElementById('default-bitrate').value = this.settings.defaultBitrate;
    document.getElementById('default-speed').value = this.settings.defaultSpeed;
    document.getElementById('auto-inject').checked = this.settings.autoInject;
    document.getElementById('show-notifications').checked = this.settings.showNotifications;
    document.getElementById('save-history').checked = this.settings.saveHistory;
    document.getElementById('history-retention').value = this.settings.historyRetention;
  }

  async saveSettings(showMessage = true) {
    try {
      // Collect form data
      const newSettings = {
        webAppUrl: document.getElementById('web-app-url').value.trim(),
        openInNewTab: document.getElementById('open-in-new-tab').checked,
        defaultBitrate: document.getElementById('default-bitrate').value,
        defaultSpeed: document.getElementById('default-speed').value,
        autoInject: document.getElementById('auto-inject').checked,
        showNotifications: document.getElementById('show-notifications').checked,
        saveHistory: document.getElementById('save-history').checked,
        historyRetention: parseInt(document.getElementById('history-retention').value)
      };

      // Validate Web App URL
      if (newSettings.webAppUrl && !this.isValidUrl(newSettings.webAppUrl)) {
        this.showStatus('Please enter a valid Web App URL', 'error');
        return;
      }

      // Save to storage
      await chrome.storage.sync.set(newSettings);
      this.settings = newSettings;

      if (showMessage) {
        this.showStatus('Settings saved successfully!', 'success');
      }

      // Notify content scripts of settings change
      chrome.tabs.query({}, (tabs) => {
        tabs.forEach(tab => {
          if (tab.url && tab.url.includes('youtube.com')) {
            chrome.tabs.sendMessage(tab.id, {
              type: 'SETTINGS_UPDATED',
              settings: newSettings
            }).catch(() => {
              // Ignore errors for tabs that don't have content script
            });
          }
        });
      });

    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showStatus('Failed to save settings. Please try again.', 'error');
    }
  }

  async resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      try {
        await chrome.storage.sync.clear();
        this.settings = { ...this.defaultSettings };
        this.updateUI();
        this.showStatus('Settings reset to defaults', 'info');
      } catch (error) {
        console.error('Failed to reset settings:', error);
        this.showStatus('Failed to reset settings', 'error');
      }
    }
  }

  async testConnection() {
    const webAppUrl = document.getElementById('web-app-url').value.trim();

    if (!webAppUrl) {
      this.showStatus('Please enter a Web App URL first', 'error');
      return;
    }

    if (!this.isValidUrl(webAppUrl)) {
      this.showStatus('Please enter a valid Web App URL', 'error');
      return;
    }

    const testButton = document.getElementById('test-connection');
    const originalText = testButton.textContent;
    testButton.textContent = 'Testing...';
    testButton.disabled = true;

    try {
      // Test web app connection
      const response = await fetch(webAppUrl, {
        method: 'GET',
        mode: 'no-cors' // Allow testing even if CORS is not configured
      });

      // Since we're using no-cors, we can't read the response
      // But if the fetch doesn't throw an error, the URL is reachable
      this.showStatus('Connection successful! Web app is reachable.', 'success');

    } catch (error) {
      console.error('Connection test failed:', error);

      // Try to open the URL in a new tab as a fallback test
      try {
        const testTab = await chrome.tabs.create({ url: webAppUrl, active: false });

        // Wait a moment then close the test tab
        setTimeout(() => {
          chrome.tabs.remove(testTab.id);
        }, 2000);

        this.showStatus('Web app URL opened successfully in test tab.', 'success');
      } catch (tabError) {
        this.showStatus(`Connection failed: ${error.message}`, 'error');
      }
    } finally {
      testButton.textContent = originalText;
      testButton.disabled = false;
    }
  }

  async clearHistory() {
    if (confirm('Are you sure you want to clear all conversion history? This cannot be undone.')) {
      try {
        await chrome.storage.local.remove(['conversionHistory']);
        this.showStatus('Conversion history cleared', 'info');
        
        // Notify background script
        chrome.runtime.sendMessage({
          type: 'HISTORY_CLEARED'
        });
      } catch (error) {
        console.error('Failed to clear history:', error);
        this.showStatus('Failed to clear history', 'error');
      }
    }
  }

  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  showStatus(message, type = 'info') {
    const statusElement = document.getElementById('status-message');
    statusElement.textContent = message;
    statusElement.className = `status-message ${type}`;
    statusElement.style.display = 'block';

    // Auto-hide after 5 seconds
    setTimeout(() => {
      statusElement.style.display = 'none';
    }, 5000);

    // Scroll to status message
    statusElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
}

// Initialize options page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new OptionsController();
});
