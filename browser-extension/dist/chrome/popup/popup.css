/* YouTube to MP3 Converter - Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8f9fa;
  color: #333;
  line-height: 1.5;
}

.popup-container {
  width: 380px;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.logo svg {
  width: 24px;
  height: 24px;
}

/* Main Content */
.popup-content {
  padding: 20px;
}

/* Input Section */
.input-section {
  margin-bottom: 20px;
}

.input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.input-group {
  display: flex;
  gap: 8px;
}

#youtube-url {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

#youtube-url:focus {
  outline: none;
  border-color: #667eea;
}

#youtube-url.valid {
  border-color: #4ecdc4;
}

#youtube-url.invalid {
  border-color: #ff6b6b;
}

.paste-btn {
  padding: 12px;
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.paste-btn:hover {
  background: #e9ecef;
  border-color: #667eea;
}

.paste-btn svg {
  width: 16px;
  height: 16px;
  color: #666;
}

/* Options Section */
.options-section {
  margin-bottom: 20px;
}

.option-group {
  margin-bottom: 16px;
}

.option-group:last-child {
  margin-bottom: 0;
}

.option-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.option-group select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
}

.option-group select:focus {
  outline: none;
  border-color: #667eea;
}

/* Action Section */
.action-section {
  margin-bottom: 20px;
}

.convert-btn {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.convert-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.convert-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.convert-btn svg {
  width: 16px;
  height: 16px;
}

/* Progress Section */
.progress-section {
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* Status Section */
.status-section {
  margin-bottom: 20px;
}

.status-message {
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
}

.status-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Footer */
.popup-footer {
  background: #f8f9fa;
  padding: 12px 20px;
  border-top: 1px solid #e1e5e9;
}

.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links a {
  color: #667eea;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: #764ba2;
}

/* Animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .popup-container {
    width: 100%;
    min-height: 100vh;
    border-radius: 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: #1a1a1a;
    color: #e0e0e0;
  }

  .popup-container {
    background: #2d2d2d;
  }

  #youtube-url,
  .option-group select {
    background: #3a3a3a;
    color: #e0e0e0;
    border-color: #555;
  }

  .paste-btn {
    background: #3a3a3a;
    border-color: #555;
  }

  .paste-btn svg {
    color: #ccc;
  }

  .popup-footer {
    background: #3a3a3a;
    border-color: #555;
  }

  .progress-bar {
    background: #555;
  }
}
