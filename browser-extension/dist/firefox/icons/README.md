# Extension Icons

This directory contains the icons for the YouTube to MP3 Converter browser extension.

## Required Icons

The extension needs the following icon sizes:

- `icon16.png` - 16x16 pixels (toolbar icon, small)
- `icon32.png` - 32x32 pixels (toolbar icon, medium)  
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store, large displays)

## Icon Guidelines

### Design Requirements
- **Theme**: Music/audio conversion theme
- **Colors**: Use the brand colors from the web app (gradients with blues, purples)
- **Style**: Modern, clean, recognizable
- **Elements**: Consider incorporating:
  - Musical note symbol
  - Download arrow
  - YouTube play button reference
  - Audio waveform

### Technical Requirements
- **Format**: PNG with transparency
- **Quality**: High resolution, crisp edges
- **Background**: Transparent
- **Padding**: Small padding around the main icon element

## Creating Icons

You can create these icons using:
- Adobe Illustrator/Photoshop
- Figma
- Canva
- Online icon generators
- SVG to PNG converters

## Placeholder Icons

Currently, this directory contains placeholder descriptions. To complete the extension:

1. Create or commission the required icon files
2. Save them in this directory with the exact filenames listed above
3. Ensure they meet the size and quality requirements
4. Test the icons in the browser extension

## Icon Preview

The icons will appear in:
- Browser toolbar (16px, 32px)
- Extension popup (32px, 48px)
- Extension management page (48px)
- Browser extension store (128px)
- Context menus (16px)

## Brand Consistency

Ensure the icons match the visual style of:
- The main web application
- The extension popup interface
- The overall YouTube to MP3 Converter brand
