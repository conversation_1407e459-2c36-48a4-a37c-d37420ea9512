<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle with Gradient -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient1)"/>
  
  <!-- YouTube Play Button Reference -->
  <path d="M45 35L85 64L45 93V35Z" fill="white" opacity="0.9"/>
  
  <!-- Musical Note -->
  <path d="M75 25C77.2091 25 79 26.7909 79 29V65C79 70.5228 74.5228 75 69 75C63.4772 75 59 70.5228 59 65C59 59.4772 63.4772 55 69 55C70.6569 55 72.2091 55.3739 73.5858 56.0307V29C73.5858 26.7909 75.3767 25 77.5858 25H75Z" fill="white"/>
  
  <!-- Download Arrow -->
  <path d="M95 85L95 95C95 97.2091 93.2091 99 91 99L37 99C34.7909 99 33 97.2091 33 95L33 85" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <path d="M64 75L64 105" stroke="white" stroke-width="4" stroke-linecap="round"/>
  <path d="M54 95L64 105L74 95" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Audio Waveform -->
  <rect x="20" y="50" width="3" height="28" fill="white" opacity="0.7" rx="1.5"/>
  <rect x="26" y="45" width="3" height="38" fill="white" opacity="0.7" rx="1.5"/>
  <rect x="32" y="40" width="3" height="48" fill="white" opacity="0.7" rx="1.5"/>
  <rect x="38" y="48" width="3" height="32" fill="white" opacity="0.7" rx="1.5"/>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
