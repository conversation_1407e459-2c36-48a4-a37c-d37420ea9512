{"name": "youtube-mp3-converter-extension", "version": "1.0.0", "description": "Browser extension for converting YouTube videos to MP3", "main": "build/build.js", "scripts": {"build": "node build/build.js", "dev": "npm run build && npm run watch", "watch": "nodemon --watch . --ext js,css,html,json --ignore dist --ignore node_modules --exec 'npm run build'", "clean": "rm -rf dist", "lint": "eslint . --ext .js", "test": "echo \"No tests specified\" && exit 0", "icons": "node build/generate-icons.js", "test-page": "open test/test.html || start test/test.html || xdg-open test/test.html", "package": "npm run build && npm run zip", "zip": "cd dist && zip -r ../chrome-extension.zip chrome/ && zip -r ../firefox-extension.zip firefox/"}, "keywords": ["youtube", "mp3", "converter", "browser-extension", "chrome", "firefox", "edge"], "author": "YouTube MP3 Converter Team", "license": "MIT", "devDependencies": {"archiver": "^5.3.1", "eslint": "^8.0.0", "nodemon": "^3.0.0", "crc-32": "^1.2.2"}, "optionalDependencies": {"sharp": "^0.32.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/youtube-mp3-converter"}, "bugs": {"url": "https://github.com/your-username/youtube-mp3-converter/issues"}, "homepage": "https://github.com/your-username/youtube-mp3-converter#readme"}