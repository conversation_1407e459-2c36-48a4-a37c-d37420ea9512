<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Extension Parameter Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 30px;
      text-align: center;
    }
    
    .test-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #667eea;
    }
    
    .param-display {
      background: white;
      padding: 15px;
      border-radius: 6px;
      margin: 10px 0;
      border: 1px solid #ddd;
      font-family: monospace;
    }
    
    .param-name {
      font-weight: bold;
      color: #667eea;
    }
    
    .param-value {
      color: #333;
      margin-left: 10px;
    }
    
    .status {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      margin: 5px;
    }
    
    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .example-urls {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }
    
    .example-url {
      font-family: monospace;
      background: white;
      padding: 8px;
      margin: 5px 0;
      border-radius: 4px;
      word-break: break-all;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔗 Extension Parameter Test</h1>
    <p>Test URL parameter passing from browser extension</p>
  </div>

  <div class="test-section">
    <h3>📋 Current URL Parameters</h3>
    <div id="params-display">
      <p>Loading URL parameters...</p>
    </div>
    <button onclick="refreshParams()">Refresh Parameters</button>
    <button onclick="clearParams()">Clear URL</button>
  </div>

  <div class="test-section">
    <h3>🧪 Test URLs</h3>
    <p>Click these links to simulate extension redirects:</p>
    
    <div class="example-urls">
      <p><strong>Basic Test:</strong></p>
      <div class="example-url">
        <a href="?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ&bitrate=192&speed=1.0&source=extension">
          Basic YouTube Video
        </a>
      </div>
      
      <p><strong>High Quality:</strong></p>
      <div class="example-url">
        <a href="?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ&bitrate=320&speed=1.0&source=extension-popup&title=Rick%20Astley%20-%20Never%20Gonna%20Give%20You%20Up">
          High Quality with Title
        </a>
      </div>
      
      <p><strong>Fast Speed:</strong></p>
      <div class="example-url">
        <a href="?url=https://www.youtube.com/shorts/Tx387XYIKZg&bitrate=192&speed=2.0&source=extension">
          YouTube Shorts at 2x Speed
        </a>
      </div>
      
      <p><strong>Short URL:</strong></p>
      <div class="example-url">
        <a href="?url=https://youtu.be/dQw4w9WgXcQ&bitrate=128&speed=1.5&source=extension">
          Short URL Format
        </a>
      </div>
    </div>
  </div>

  <div class="test-section">
    <h3>✅ Expected Behavior</h3>
    <ul>
      <li><strong>URL Parameter:</strong> Should contain a valid YouTube URL</li>
      <li><strong>Bitrate:</strong> Should be 128, 192, or 320</li>
      <li><strong>Speed:</strong> Should be 1.0, 1.5, or 2.0</li>
      <li><strong>Source:</strong> Should indicate "extension" or "extension-popup"</li>
      <li><strong>Title:</strong> Optional video title (URL encoded)</li>
    </ul>
  </div>

  <div class="test-section">
    <h3>🔧 Integration Notes</h3>
    <div id="integration-info">
      <p><strong>For Web App Integration:</strong></p>
      <ol>
        <li>Parse URL parameters on page load</li>
        <li>Pre-fill the YouTube URL input field</li>
        <li>Set bitrate and speed options</li>
        <li>Show success message if source=extension</li>
        <li>Clear URL parameters after processing</li>
      </ol>
      
      <p><strong>JavaScript Example:</strong></p>
      <div class="param-display">
const urlParams = new URLSearchParams(window.location.search);<br>
const videoUrl = urlParams.get('url');<br>
const bitrate = urlParams.get('bitrate');<br>
const speed = urlParams.get('speed');<br>
const source = urlParams.get('source');<br>
const title = urlParams.get('title');
      </div>
    </div>
  </div>

  <script>
    function displayParams() {
      const urlParams = new URLSearchParams(window.location.search);
      const paramsDisplay = document.getElementById('params-display');
      
      if (urlParams.toString() === '') {
        paramsDisplay.innerHTML = '<div class="status info">No URL parameters found</div>';
        return;
      }
      
      let html = '<div class="status success">URL parameters detected:</div>';
      
      const params = ['url', 'bitrate', 'speed', 'source', 'title'];
      params.forEach(param => {
        const value = urlParams.get(param);
        if (value) {
          html += `
            <div class="param-display">
              <span class="param-name">${param}:</span>
              <span class="param-value">${decodeURIComponent(value)}</span>
            </div>
          `;
        }
      });
      
      // Show all parameters
      html += '<div class="param-display"><span class="param-name">Full Query String:</span><span class="param-value">' + urlParams.toString() + '</span></div>';
      
      paramsDisplay.innerHTML = html;
      
      // Validate parameters
      validateParams(urlParams);
    }
    
    function validateParams(urlParams) {
      const url = urlParams.get('url');
      const bitrate = urlParams.get('bitrate');
      const speed = urlParams.get('speed');
      
      let validationHtml = '<h4>Validation Results:</h4>';
      
      // Validate URL
      if (url) {
        const isValidYouTube = /^https?:\/\/(www\.)?(youtube\.com\/(watch\?v=|shorts\/)|youtu\.be\/)/.test(url);
        validationHtml += `<div class="status ${isValidYouTube ? 'success' : 'error'}">
          YouTube URL: ${isValidYouTube ? '✓ Valid' : '❌ Invalid'}
        </div>`;
      }
      
      // Validate bitrate
      if (bitrate) {
        const validBitrates = ['128', '192', '320'];
        const isValidBitrate = validBitrates.includes(bitrate);
        validationHtml += `<div class="status ${isValidBitrate ? 'success' : 'error'}">
          Bitrate: ${isValidBitrate ? '✓ Valid' : '❌ Invalid'} (${bitrate})
        </div>`;
      }
      
      // Validate speed
      if (speed) {
        const validSpeeds = ['1.0', '1.5', '2.0'];
        const isValidSpeed = validSpeeds.includes(speed);
        validationHtml += `<div class="status ${isValidSpeed ? 'success' : 'error'}">
          Speed: ${isValidSpeed ? '✓ Valid' : '❌ Invalid'} (${speed})
        </div>`;
      }
      
      document.getElementById('params-display').innerHTML += validationHtml;
    }
    
    function refreshParams() {
      displayParams();
    }
    
    function clearParams() {
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
      displayParams();
    }
    
    // Display parameters on page load
    window.addEventListener('load', displayParams);
  </script>
</body>
</html>
