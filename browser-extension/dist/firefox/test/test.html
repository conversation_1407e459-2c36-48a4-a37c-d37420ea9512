<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube to MP3 Converter - Extension Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 30px;
      text-align: center;
    }
    
    .test-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #667eea;
    }
    
    .test-section h3 {
      margin-top: 0;
      color: #333;
    }
    
    .test-url {
      background: white;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      border: 1px solid #ddd;
      margin: 10px 0;
    }
    
    .status {
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      margin: 5px;
    }
    
    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .test-results {
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🎵 YouTube to MP3 Converter</h1>
    <p>Browser Extension Test Page</p>
  </div>

  <div class="test-section">
    <h3>📋 Extension Installation Check</h3>
    <p>This page helps you test if the YouTube to MP3 Converter browser extension is working correctly.</p>
    <div id="extension-status" class="status info">
      Checking extension status...
    </div>
    <button onclick="checkExtension()">Check Extension</button>
  </div>

  <div class="test-section">
    <h3>🔗 Test YouTube URLs</h3>
    <p>Use these sample YouTube URLs to test the extension functionality:</p>
    
    <div>
      <strong>Regular Video:</strong>
      <div class="test-url">https://www.youtube.com/watch?v=dQw4w9WgXcQ</div>
      <button onclick="openTestUrl('https://www.youtube.com/watch?v=dQw4w9WgXcQ')">Test This URL</button>
    </div>
    
    <div>
      <strong>YouTube Shorts:</strong>
      <div class="test-url">https://www.youtube.com/shorts/Tx387XYIKZg</div>
      <button onclick="openTestUrl('https://www.youtube.com/shorts/Tx387XYIKZg')">Test This URL</button>
    </div>
    
    <div>
      <strong>Short URL:</strong>
      <div class="test-url">https://youtu.be/dQw4w9WgXcQ</div>
      <button onclick="openTestUrl('https://youtu.be/dQw4w9WgXcQ')">Test This URL</button>
    </div>
  </div>

  <div class="test-section">
    <h3>⚙️ Extension Features to Test</h3>
    <ul>
      <li><strong>Convert Button:</strong> Look for a "Convert to MP3" button below the video player</li>
      <li><strong>Popup Interface:</strong> Click the extension icon in your browser toolbar</li>
      <li><strong>Context Menu:</strong> Right-click on YouTube video links</li>
      <li><strong>Settings:</strong> Right-click the extension icon and select "Options"</li>
      <li><strong>Conversion Options:</strong> Test different bitrates and speeds</li>
      <li><strong>Progress Display:</strong> Watch the conversion progress animation</li>
    </ul>
  </div>

  <div class="test-section">
    <h3>🔧 Troubleshooting</h3>
    <div id="troubleshooting-info">
      <p><strong>If the extension isn't working:</strong></p>
      <ol>
        <li>Make sure the extension is installed and enabled</li>
        <li>Check that you're using a supported browser (Chrome, Edge, Firefox)</li>
        <li>Verify the extension has permission to access YouTube</li>
        <li>Try refreshing the YouTube page</li>
        <li>Check the browser console for error messages</li>
      </ol>
      
      <p><strong>Common Issues:</strong></p>
      <ul>
        <li><strong>Button not appearing:</strong> Extension may not be injected properly</li>
        <li><strong>Conversion fails:</strong> API endpoint may not be configured</li>
        <li><strong>No download:</strong> Check browser download settings</li>
      </ul>
    </div>
  </div>

  <div class="test-section">
    <h3>📊 Test Results</h3>
    <div id="test-results" class="test-results">
      <p>Test results will appear here...</p>
    </div>
    <button onclick="runAllTests()">Run All Tests</button>
    <button onclick="clearResults()">Clear Results</button>
  </div>

  <script>
    function checkExtension() {
      const statusDiv = document.getElementById('extension-status');
      
      // Check if extension is available
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
        statusDiv.className = 'status success';
        statusDiv.textContent = '✅ Extension detected and running!';
        
        // Try to communicate with extension
        chrome.runtime.sendMessage({type: 'PING'}, (response) => {
          if (chrome.runtime.lastError) {
            statusDiv.className = 'status error';
            statusDiv.textContent = '❌ Extension detected but not responding: ' + chrome.runtime.lastError.message;
          } else {
            statusDiv.className = 'status success';
            statusDiv.textContent = '✅ Extension is working correctly!';
          }
        });
      } else {
        statusDiv.className = 'status error';
        statusDiv.textContent = '❌ Extension not detected. Please install the extension first.';
      }
    }

    function openTestUrl(url) {
      window.open(url, '_blank');
      addTestResult(`Opened test URL: ${url}`);
    }

    function addTestResult(message) {
      const resultsDiv = document.getElementById('test-results');
      const timestamp = new Date().toLocaleTimeString();
      resultsDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
    }

    function runAllTests() {
      addTestResult('🚀 Starting extension tests...');
      
      // Test 1: Extension detection
      checkExtension();
      addTestResult('✓ Extension detection test completed');
      
      // Test 2: URL validation
      const testUrls = [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'https://www.youtube.com/shorts/Tx387XYIKZg',
        'https://youtu.be/dQw4w9WgXcQ'
      ];
      
      testUrls.forEach(url => {
        const isValid = /^https?:\/\/(www\.)?(youtube\.com\/(watch\?v=|shorts\/)|youtu\.be\/)/.test(url);
        addTestResult(`${isValid ? '✓' : '❌'} URL validation: ${url}`);
      });
      
      // Test 3: Storage access
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.sync.get(['defaultBitrate'], (result) => {
          addTestResult('✓ Storage access test completed');
        });
      } else {
        addTestResult('❌ Storage access not available');
      }
      
      addTestResult('🏁 All tests completed!');
    }

    function clearResults() {
      document.getElementById('test-results').innerHTML = '<p>Test results cleared...</p>';
    }

    // Auto-check extension on page load
    window.addEventListener('load', () => {
      setTimeout(checkExtension, 1000);
    });
  </script>
</body>
</html>
