# lodash.union v4.6.0

The [lodash](https://lodash.com/) method `_.union` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.union
```

In Node.js:
```js
var union = require('lodash.union');
```

See the [documentation](https://lodash.com/docs#union) or [package source](https://github.com/lodash/lodash/blob/4.6.0-npm-packages/lodash.union) for more details.
