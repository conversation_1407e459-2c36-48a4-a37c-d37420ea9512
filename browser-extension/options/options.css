/* YouTube to MP3 Converter - Options Page Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #333;
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo svg {
  width: 32px;
  height: 32px;
}

.logo h1 {
  font-size: 24px;
  font-weight: 600;
}

.version {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

/* Main Content */
.main-content {
  padding: 32px;
}

.settings-section {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #e1e5e9;
}

.settings-section:last-child {
  border-bottom: none;
}

.settings-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

/* Form Elements */
label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.input-field,
.select-field {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: white;
}

.input-field:focus,
.select-field:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.help-text {
  margin-top: 6px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 0;
  font-weight: normal;
  margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e5e9;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Buttons */
.primary-button,
.secondary-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 12px;
  margin-bottom: 12px;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.secondary-button {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.secondary-button:hover {
  background: #e9ecef;
  border-color: #667eea;
  color: #667eea;
}

.secondary-button.danger {
  color: #dc3545;
  border-color: #dc3545;
}

.secondary-button.danger:hover {
  background: #dc3545;
  color: white;
}

/* Actions Section */
.actions-section {
  margin-top: 40px;
  padding-top: 32px;
  border-top: 2px solid #e1e5e9;
  text-align: center;
}

/* Status Messages */
.status-message {
  margin-top: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
}

.status-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Footer */
.footer {
  background: #f8f9fa;
  padding: 24px 32px;
  border-top: 1px solid #e1e5e9;
  margin-top: 40px;
}

.footer-links {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s;
}

.footer-links a:hover {
  color: #764ba2;
}

.footer-text {
  font-size: 13px;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    margin: 0;
    border-radius: 0;
  }

  .header {
    padding: 20px 24px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .main-content {
    padding: 24px 20px;
  }

  .footer {
    padding: 20px 24px;
  }

  .footer-links {
    flex-direction: column;
    gap: 12px;
  }

  .actions-section {
    text-align: left;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    margin-right: 0;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #e0e0e0;
  }

  .container {
    background: #2d2d2d;
  }

  .settings-section {
    border-color: #444;
  }

  .input-field,
  .select-field {
    background: #3a3a3a;
    border-color: #555;
    color: #e0e0e0;
  }

  .input-field:focus,
  .select-field:focus {
    border-color: #667eea;
  }

  .secondary-button {
    background: #3a3a3a;
    border-color: #555;
    color: #ccc;
  }

  .secondary-button:hover {
    background: #444;
  }

  .footer {
    background: #3a3a3a;
    border-color: #555;
  }

  .checkmark {
    border-color: #555;
  }

  .help-text {
    color: #aaa;
  }

  label {
    color: #ccc;
  }

  .settings-section h2 {
    color: #e0e0e0;
  }
}
