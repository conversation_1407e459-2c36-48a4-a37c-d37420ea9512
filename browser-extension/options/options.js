// YouTube to MP3 Converter - Options Page Script

class OptionsController {
  constructor() {
    this.defaultSettings = {
      apiUrl: 'https://your-api-domain.com',
      apiKey: '',
      defaultBitrate: '192',
      defaultSpeed: '1.0',
      autoInject: true,
      showNotifications: true,
      saveHistory: true,
      autoDownload: false,
      historyRetention: 30
    };
    
    this.init();
  }

  async init() {
    await this.loadSettings();
    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    // Save settings button
    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    // Reset settings button
    document.getElementById('reset-settings').addEventListener('click', () => {
      this.resetSettings();
    });

    // Test connection button
    document.getElementById('test-connection').addEventListener('click', () => {
      this.testConnection();
    });

    // Clear history button
    document.getElementById('clear-history').addEventListener('click', () => {
      this.clearHistory();
    });

    // Footer links
    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/youtube-mp3-converter#readme' });
    });

    document.getElementById('github-link').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/youtube-mp3-converter' });
    });

    document.getElementById('report-issue').addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/youtube-mp3-converter/issues' });
    });

    // Auto-save on input changes
    this.setupAutoSave();
  }

  setupAutoSave() {
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
      input.addEventListener('change', () => {
        this.saveSettings(false); // Save without showing message
      });
    });
  }

  async loadSettings() {
    try {
      const stored = await chrome.storage.sync.get(Object.keys(this.defaultSettings));
      this.settings = { ...this.defaultSettings, ...stored };
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.settings = { ...this.defaultSettings };
    }
  }

  updateUI() {
    // Update form fields with current settings
    document.getElementById('api-url').value = this.settings.apiUrl;
    document.getElementById('api-key').value = this.settings.apiKey;
    document.getElementById('default-bitrate').value = this.settings.defaultBitrate;
    document.getElementById('default-speed').value = this.settings.defaultSpeed;
    document.getElementById('auto-inject').checked = this.settings.autoInject;
    document.getElementById('show-notifications').checked = this.settings.showNotifications;
    document.getElementById('save-history').checked = this.settings.saveHistory;
    document.getElementById('auto-download').checked = this.settings.autoDownload;
    document.getElementById('history-retention').value = this.settings.historyRetention;
  }

  async saveSettings(showMessage = true) {
    try {
      // Collect form data
      const newSettings = {
        apiUrl: document.getElementById('api-url').value.trim(),
        apiKey: document.getElementById('api-key').value.trim(),
        defaultBitrate: document.getElementById('default-bitrate').value,
        defaultSpeed: document.getElementById('default-speed').value,
        autoInject: document.getElementById('auto-inject').checked,
        showNotifications: document.getElementById('show-notifications').checked,
        saveHistory: document.getElementById('save-history').checked,
        autoDownload: document.getElementById('auto-download').checked,
        historyRetention: parseInt(document.getElementById('history-retention').value)
      };

      // Validate API URL
      if (newSettings.apiUrl && !this.isValidUrl(newSettings.apiUrl)) {
        this.showStatus('Please enter a valid API URL', 'error');
        return;
      }

      // Save to storage
      await chrome.storage.sync.set(newSettings);
      this.settings = newSettings;

      if (showMessage) {
        this.showStatus('Settings saved successfully!', 'success');
      }

      // Notify background script of settings change
      chrome.runtime.sendMessage({
        type: 'SETTINGS_UPDATED',
        settings: newSettings
      });

    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showStatus('Failed to save settings. Please try again.', 'error');
    }
  }

  async resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      try {
        await chrome.storage.sync.clear();
        this.settings = { ...this.defaultSettings };
        this.updateUI();
        this.showStatus('Settings reset to defaults', 'info');
      } catch (error) {
        console.error('Failed to reset settings:', error);
        this.showStatus('Failed to reset settings', 'error');
      }
    }
  }

  async testConnection() {
    const apiUrl = document.getElementById('api-url').value.trim();
    
    if (!apiUrl) {
      this.showStatus('Please enter an API URL first', 'error');
      return;
    }

    if (!this.isValidUrl(apiUrl)) {
      this.showStatus('Please enter a valid API URL', 'error');
      return;
    }

    const testButton = document.getElementById('test-connection');
    const originalText = testButton.textContent;
    testButton.textContent = 'Testing...';
    testButton.disabled = true;

    try {
      // Test API connection
      const response = await fetch(`${apiUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.showStatus(`Connection successful! API version: ${data.version || 'unknown'}`, 'success');
      } else {
        this.showStatus(`Connection failed: ${response.status} ${response.statusText}`, 'error');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      this.showStatus(`Connection failed: ${error.message}`, 'error');
    } finally {
      testButton.textContent = originalText;
      testButton.disabled = false;
    }
  }

  async clearHistory() {
    if (confirm('Are you sure you want to clear all conversion history? This cannot be undone.')) {
      try {
        await chrome.storage.local.remove(['conversionHistory']);
        this.showStatus('Conversion history cleared', 'info');
        
        // Notify background script
        chrome.runtime.sendMessage({
          type: 'HISTORY_CLEARED'
        });
      } catch (error) {
        console.error('Failed to clear history:', error);
        this.showStatus('Failed to clear history', 'error');
      }
    }
  }

  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  showStatus(message, type = 'info') {
    const statusElement = document.getElementById('status-message');
    statusElement.textContent = message;
    statusElement.className = `status-message ${type}`;
    statusElement.style.display = 'block';

    // Auto-hide after 5 seconds
    setTimeout(() => {
      statusElement.style.display = 'none';
    }, 5000);

    // Scroll to status message
    statusElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
}

// Initialize options page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new OptionsController();
});
