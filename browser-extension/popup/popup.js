// YouTube to MP3 Converter - Popup Script

class PopupController {
  constructor() {
    this.apiUrl = 'https://your-api-domain.com'; // Replace with actual API URL
    this.currentTab = null;
    this.init();
  }

  async init() {
    // Get current tab info
    await this.getCurrentTab();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Auto-fill URL if on YouTube
    this.autoFillYouTubeUrl();
    
    // Load saved settings
    this.loadSettings();
  }

  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
    } catch (error) {
      console.error('Error getting current tab:', error);
    }
  }

  setupEventListeners() {
    // URL input validation
    const urlInput = document.getElementById('youtube-url');
    urlInput.addEventListener('input', () => this.validateUrl());
    urlInput.addEventListener('paste', () => {
      setTimeout(() => this.validateUrl(), 100);
    });

    // Paste button
    document.getElementById('paste-btn').addEventListener('click', () => {
      this.pasteFromClipboard();
    });

    // Convert button
    document.getElementById('convert-btn').addEventListener('click', () => {
      this.startConversion();
    });

    // Footer links
    document.getElementById('history-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.openHistoryPage();
    });

    document.getElementById('settings-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.openSettingsPage();
    });

    document.getElementById('help-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelpPage();
    });

    // Save settings on change
    document.getElementById('bitrate').addEventListener('change', () => this.saveSettings());
    document.getElementById('speed').addEventListener('change', () => this.saveSettings());
  }

  autoFillYouTubeUrl() {
    if (this.currentTab && this.isYouTubeUrl(this.currentTab.url)) {
      const urlInput = document.getElementById('youtube-url');
      urlInput.value = this.currentTab.url;
      this.validateUrl();
    }
  }

  isYouTubeUrl(url) {
    const youtubeRegex = /^https?:\/\/(www\.)?(youtube\.com\/(watch\?v=|shorts\/)|youtu\.be\/)/;
    return youtubeRegex.test(url);
  }

  validateUrl() {
    const urlInput = document.getElementById('youtube-url');
    const convertBtn = document.getElementById('convert-btn');
    const url = urlInput.value.trim();

    if (!url) {
      urlInput.className = '';
      convertBtn.disabled = true;
      return;
    }

    if (this.isYouTubeUrl(url)) {
      urlInput.className = 'valid';
      convertBtn.disabled = false;
    } else {
      urlInput.className = 'invalid';
      convertBtn.disabled = true;
    }
  }

  async pasteFromClipboard() {
    try {
      const text = await navigator.clipboard.readText();
      const urlInput = document.getElementById('youtube-url');
      urlInput.value = text;
      this.validateUrl();
    } catch (error) {
      console.error('Failed to read clipboard:', error);
      this.showStatus('Failed to access clipboard', 'error');
    }
  }

  async startConversion() {
    const url = document.getElementById('youtube-url').value.trim();
    const bitrate = document.getElementById('bitrate').value;
    const speed = document.getElementById('speed').value;

    if (!this.isYouTubeUrl(url)) {
      this.showStatus('Please enter a valid YouTube URL', 'error');
      return;
    }

    // Show progress
    this.showProgress();
    
    // Disable convert button
    const convertBtn = document.getElementById('convert-btn');
    convertBtn.disabled = true;
    convertBtn.textContent = 'Converting...';

    try {
      // Extract video ID
      const videoId = this.extractVideoId(url);
      if (!videoId) {
        throw new Error('Could not extract video ID from URL');
      }

      // Start conversion process
      await this.performConversion(videoId, bitrate, speed);
      
      // Show success
      this.showStatus('Conversion completed! Download will start shortly.', 'success');
      
      // Save to history
      this.saveToHistory(url, bitrate, speed);

    } catch (error) {
      console.error('Conversion error:', error);
      this.showStatus('Conversion failed: ' + error.message, 'error');
    } finally {
      // Re-enable button
      convertBtn.disabled = false;
      convertBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
        </svg>
        Convert to MP3
      `;
      
      // Hide progress after delay
      setTimeout(() => this.hideProgress(), 3000);
    }
  }

  extractVideoId(url) {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  async performConversion(videoId, bitrate, speed) {
    // Simulate conversion process with progress updates
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    const steps = [
      { progress: 20, text: 'Downloading video...', delay: 1000 },
      { progress: 50, text: 'Converting to MP3...', delay: 1500 },
      { progress: 80, text: 'Optimizing audio...', delay: 1000 },
      { progress: 100, text: 'Preparing download...', delay: 500 }
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, step.delay));
      progressFill.style.width = step.progress + '%';
      progressText.textContent = step.text;
    }

    // In a real implementation, you would make API calls here
    // Example:
    // const response = await fetch(`${this.apiUrl}/convert`, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ videoId, bitrate, speed })
    // });
    // 
    // if (!response.ok) {
    //   throw new Error('Conversion failed');
    // }
    // 
    // const result = await response.json();
    // return result;
  }

  showProgress() {
    const progressSection = document.getElementById('progress-section');
    const statusSection = document.getElementById('status-section');
    
    progressSection.style.display = 'block';
    statusSection.style.display = 'none';
    
    // Reset progress
    document.getElementById('progress-fill').style.width = '0%';
    document.getElementById('progress-text').textContent = 'Preparing conversion...';
  }

  hideProgress() {
    const progressSection = document.getElementById('progress-section');
    progressSection.style.display = 'none';
  }

  showStatus(message, type = 'info') {
    const statusSection = document.getElementById('status-section');
    const statusMessage = document.getElementById('status-message');
    
    statusMessage.textContent = message;
    statusMessage.className = `status-message ${type}`;
    statusSection.style.display = 'block';
    
    // Auto-hide after 5 seconds for non-error messages
    if (type !== 'error') {
      setTimeout(() => {
        statusSection.style.display = 'none';
      }, 5000);
    }
  }

  async saveToHistory(url, bitrate, speed) {
    try {
      const historyItem = {
        url,
        bitrate,
        speed,
        timestamp: Date.now(),
        status: 'completed'
      };

      // Get existing history
      const result = await chrome.storage.local.get(['conversionHistory']);
      const history = result.conversionHistory || [];
      
      // Add new item to beginning
      history.unshift(historyItem);
      
      // Keep only last 50 items
      const trimmedHistory = history.slice(0, 50);
      
      // Save back to storage
      await chrome.storage.local.set({ conversionHistory: trimmedHistory });
    } catch (error) {
      console.error('Failed to save to history:', error);
    }
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['defaultBitrate', 'defaultSpeed']);
      
      if (result.defaultBitrate) {
        document.getElementById('bitrate').value = result.defaultBitrate;
      }
      
      if (result.defaultSpeed) {
        document.getElementById('speed').value = result.defaultSpeed;
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async saveSettings() {
    try {
      const bitrate = document.getElementById('bitrate').value;
      const speed = document.getElementById('speed').value;
      
      await chrome.storage.sync.set({
        defaultBitrate: bitrate,
        defaultSpeed: speed
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  openHistoryPage() {
    chrome.tabs.create({ url: 'https://your-web-app.com/history' });
    window.close();
  }

  openSettingsPage() {
    chrome.tabs.create({ url: 'https://your-web-app.com/settings' });
    window.close();
  }

  openHelpPage() {
    chrome.tabs.create({ url: 'https://your-web-app.com/help' });
    window.close();
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
