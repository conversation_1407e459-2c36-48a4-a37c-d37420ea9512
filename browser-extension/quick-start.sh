#!/bin/bash

# YouTube to MP3 Converter - Quick Start Script
# This script helps you quickly set up and build the browser extension

echo "🚀 YouTube to MP3 Converter - Quick Start"
echo "=========================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm found: $(npm --version)"
echo ""

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"
echo ""

# Generate icons
echo "🎨 Generating extension icons..."
npm run icons

echo "✅ Icons generated"
echo ""

# Build extension
echo "🔨 Building extension for all browsers..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Extension built successfully"
echo ""

# Show results
echo "🎉 Setup completed successfully!"
echo ""
echo "📁 Generated files:"
echo "   - dist/chrome/     (Chrome/Edge extension)"
echo "   - dist/firefox/    (Firefox extension)"
echo "   - test/test.html   (Test page)"
echo ""

echo "📋 Next steps:"
echo ""
echo "1. 🔧 Configure API endpoint:"
echo "   - Edit the API URL in content/content.js, popup/popup.js, and background/background.js"
echo "   - Replace 'https://your-api-domain.com' with your actual API URL"
echo ""

echo "2. 📱 Install in browser:"
echo "   Chrome/Edge:"
echo "   - Go to chrome://extensions/ or edge://extensions/"
echo "   - Enable 'Developer mode'"
echo "   - Click 'Load unpacked' and select the 'dist/chrome' folder"
echo ""
echo "   Firefox:"
echo "   - Go to about:debugging"
echo "   - Click 'This Firefox'"
echo "   - Click 'Load Temporary Add-on'"
echo "   - Select 'dist/firefox/manifest.json'"
echo ""

echo "3. 🧪 Test the extension:"
echo "   - Open test/test.html in your browser"
echo "   - Visit any YouTube video page"
echo "   - Look for the 'Convert to MP3' button"
echo ""

echo "4. ⚙️ Configure settings:"
echo "   - Right-click the extension icon"
echo "   - Select 'Options' to configure API endpoint and preferences"
echo ""

echo "🔗 Useful commands:"
echo "   npm run dev      - Build and watch for changes"
echo "   npm run clean    - Clean build directory"
echo "   npm run lint     - Run code linting"
echo "   npm run test-page - Open test page"
echo ""

echo "📚 Documentation:"
echo "   - README.md           - Full documentation"
echo "   - INSTALLATION.md     - Detailed installation guide"
echo "   - options/options.html - Settings page"
echo ""

echo "🐛 Troubleshooting:"
echo "   - Check browser console for errors"
echo "   - Verify API endpoint is accessible"
echo "   - Ensure extension permissions are granted"
echo "   - Try refreshing YouTube pages"
echo ""

echo "✨ Happy converting! 🎵"
