# Database Setup - Supabase

This directory contains the database schema and configuration for the YouTube to MP3 Converter application.

## 🗄️ Database Schema

### Tables

#### `users`
Extends the default Supabase auth.users table with additional profile information.
- `id` - UUID (references auth.users.id)
- `email` - User's email address
- `display_name` - Optional display name
- `avatar_url` - Optional avatar image URL
- `created_at` - Account creation timestamp
- `updated_at` - Last profile update timestamp

#### `conversions`
Stores all video conversion records and their status.
- `id` - UUID primary key
- `user_id` - References users.id (nullable for guest conversions)
- `youtube_url` - Original YouTube video URL
- `video_title` - Video title from YouTube
- `mp3_url` - Download URL for converted MP3 file
- `bitrate` - Audio quality (128, 192, or 320 kbps)
- `duration_factor` - Playback speed (1.0, 1.5, or 2.0)
- `status` - Conversion status (pending, processing, completed, failed)
- `progress` - Conversion progress percentage (0-100)
- `current_step` - Current processing step description
- `error_message` - Error details if conversion failed
- `file_size` - Size of converted MP3 file in bytes
- `duration` - Video duration in seconds
- `created_at` - Conversion request timestamp
- `completed_at` - Conversion completion timestamp

#### `popular_downloads`
Tracks popular videos for the "Today's Top 10" feature.
- `id` - UUID primary key
- `youtube_url` - YouTube video URL (unique)
- `video_title` - Video title
- `download_count` - Total download count
- `daily_count` - Downloads today (reset daily)
- `thumbnail_url` - Video thumbnail URL
- `last_updated` - Last update timestamp

## 🚀 Setup Instructions

### 1. Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for the project to be ready

### 2. Run Database Migration
1. Go to the SQL Editor in your Supabase dashboard
2. Copy and paste the contents of `migrations/001_initial_schema.sql`
3. Click "Run" to execute the migration

### 3. Configure Authentication
1. Go to Authentication → Settings in Supabase dashboard
2. Enable Email authentication
3. Configure Google OAuth (optional):
   - Go to Authentication → Providers
   - Enable Google provider
   - Add your Google OAuth credentials

### 4. Set Environment Variables
Add these to your `.env.local` file:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 5. Configure Row Level Security (RLS)
The migration script automatically sets up RLS policies:
- Users can only access their own data
- Popular downloads are readable by everyone
- Service role has full access for backend operations

## 🔒 Security Features

### Row Level Security (RLS)
- **Users table**: Users can only view/update their own profile
- **Conversions table**: Users can only access their own conversions
- **Popular downloads**: Read-only for all users, write access for service role

### Authentication Integration
- Automatic user profile creation when users sign up
- Secure session management with Supabase Auth
- Support for email/password and OAuth providers

## 📊 Real-time Features

### Supabase Realtime
The application uses Supabase's real-time features for:
- Live conversion progress updates
- Real-time popular downloads updates
- User conversion history updates

### Subscription Channels
- `conversion_progress:{conversionId}` - Individual conversion updates
- `user_conversions:{userId}` - User's conversion list updates
- `popular_downloads` - Popular downloads list updates

## 🛠️ Database Functions

### `update_popular_download(url, title, thumbnail)`
Updates or creates a popular download entry.

### `reset_daily_counts()`
Resets daily download counts (should be called via cron job).

### `cleanup_old_conversions(days_old)`
Removes old conversion records to keep database clean.

## 📈 Performance Optimizations

### Indexes
- `idx_conversions_user_id` - Fast user conversion lookups
- `idx_conversions_status` - Status-based queries
- `idx_conversions_created_at` - Time-based sorting
- `idx_popular_downloads_daily_count` - Popular downloads ranking
- `idx_popular_downloads_download_count` - All-time popular ranking

### Query Optimization
- Use appropriate indexes for common queries
- Limit result sets with pagination
- Use real-time subscriptions instead of polling

## 🔄 Maintenance Tasks

### Daily Tasks (via cron job)
```sql
-- Reset daily download counts
SELECT public.reset_daily_counts();
```

### Weekly Tasks
```sql
-- Clean up old conversions (older than 30 days)
SELECT public.cleanup_old_conversions(30);
```

### Monitoring Queries
```sql
-- View conversion statistics
SELECT * FROM public.conversion_stats;

-- Check recent activity
SELECT status, COUNT(*) 
FROM public.conversions 
WHERE created_at >= CURRENT_DATE 
GROUP BY status;

-- Top popular downloads
SELECT video_title, daily_count, download_count 
FROM public.popular_downloads 
ORDER BY daily_count DESC 
LIMIT 10;
```

## 🐛 Troubleshooting

### Common Issues

**RLS Policy Errors**
- Ensure user is authenticated
- Check if policies are correctly applied
- Verify service role key for backend operations

**Real-time Connection Issues**
- Check Supabase project status
- Verify real-time is enabled in project settings
- Check network connectivity

**Migration Errors**
- Ensure you have proper permissions
- Check for syntax errors in SQL
- Verify extensions are available

### Debug Queries
```sql
-- Check user permissions
SELECT auth.uid(), auth.role();

-- View RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Check table permissions
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_schema = 'public';
```

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Real-time Documentation](https://supabase.com/docs/guides/realtime)
