-- YouTube to MP3 Converter Database Schema
-- This file should be run in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    display_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create conversions table
CREATE TABLE IF NOT EXISTS public.conversions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    youtube_url TEXT NOT NULL,
    video_title TEXT NOT NULL,
    mp3_url TEXT,
    bitrate INTEGER NOT NULL CHECK (bitrate IN (128, 192, 320)),
    duration_factor DECIMAL(3,1) NOT NULL CHECK (duration_factor IN (1.0, 1.5, 2.0)),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    current_step TEXT,
    error_message TEXT,
    file_size BIGINT,
    duration INTEGER, -- in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create popular_downloads table
CREATE TABLE IF NOT EXISTS public.popular_downloads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    youtube_url TEXT NOT NULL UNIQUE,
    video_title TEXT NOT NULL,
    download_count INTEGER DEFAULT 0,
    daily_count INTEGER DEFAULT 0,
    thumbnail_url TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversions_user_id ON public.conversions(user_id);
CREATE INDEX IF NOT EXISTS idx_conversions_status ON public.conversions(status);
CREATE INDEX IF NOT EXISTS idx_conversions_created_at ON public.conversions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_popular_downloads_daily_count ON public.popular_downloads(daily_count DESC);
CREATE INDEX IF NOT EXISTS idx_popular_downloads_download_count ON public.popular_downloads(download_count DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, display_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'display_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.popular_downloads ENABLE ROW LEVEL SECURITY;

-- Users can only see and update their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Conversions policies
CREATE POLICY "Users can view own conversions" ON public.conversions
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own conversions" ON public.conversions
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own conversions" ON public.conversions
    FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

-- Service role can manage all conversions (for backend updates)
CREATE POLICY "Service role can manage conversions" ON public.conversions
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Popular downloads are readable by everyone
CREATE POLICY "Popular downloads are viewable by everyone" ON public.popular_downloads
    FOR SELECT USING (true);

-- Only service role can modify popular downloads
CREATE POLICY "Service role can manage popular downloads" ON public.popular_downloads
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create function to update popular downloads
CREATE OR REPLACE FUNCTION public.update_popular_download(
    p_youtube_url TEXT,
    p_video_title TEXT,
    p_thumbnail_url TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.popular_downloads (youtube_url, video_title, download_count, daily_count, thumbnail_url)
    VALUES (p_youtube_url, p_video_title, 1, 1, p_thumbnail_url)
    ON CONFLICT (youtube_url) 
    DO UPDATE SET
        download_count = popular_downloads.download_count + 1,
        daily_count = popular_downloads.daily_count + 1,
        video_title = p_video_title,
        thumbnail_url = COALESCE(p_thumbnail_url, popular_downloads.thumbnail_url),
        last_updated = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to reset daily counts (to be called by cron job)
CREATE OR REPLACE FUNCTION public.reset_daily_counts()
RETURNS VOID AS $$
BEGIN
    UPDATE public.popular_downloads SET daily_count = 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to cleanup old conversions
CREATE OR REPLACE FUNCTION public.cleanup_old_conversions(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.conversions 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Insert some sample data for testing (optional)
-- INSERT INTO public.popular_downloads (youtube_url, video_title, download_count, daily_count, thumbnail_url)
-- VALUES 
--     ('https://youtube.com/watch?v=dQw4w9WgXcQ', 'Rick Astley - Never Gonna Give You Up', 1000, 50, 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'),
--     ('https://youtube.com/watch?v=9bZkp7q19f0', 'PSY - GANGNAM STYLE', 800, 40, 'https://img.youtube.com/vi/9bZkp7q19f0/maxresdefault.jpg');

-- Create a view for conversion statistics (optional)
CREATE OR REPLACE VIEW public.conversion_stats AS
SELECT 
    COUNT(*) as total_conversions,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_conversions,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_conversions,
    COUNT(*) FILTER (WHERE status = 'processing') as processing_conversions,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE) as today_conversions,
    AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (completed_at - created_at)) END) as avg_processing_time_seconds
FROM public.conversions;
