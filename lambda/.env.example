# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here

# S3 Configuration
AWS_S3_BUCKET=youtube-mp3-converter-files
AWS_S3_REGION=us-east-1

# FFmpeg Configuration (if using custom paths)
FFMPEG_PATH=/usr/bin/ffmpeg
FFPROBE_PATH=/usr/bin/ffprobe

# Logging
LOG_LEVEL=1
NODE_ENV=development

# Optional: Custom timeouts (in milliseconds)
YOUTUBE_TIMEOUT=300000
FFMPEG_TIMEOUT=600000

# Optional: Custom limits
MAX_DURATION=3600
MAX_FILE_SIZE=104857600
