# YouTube to MP3 Converter - Lambda Service

AWS Lambda function for converting YouTube videos to MP3 audio files.

## 🏗️ Architecture

- **Runtime**: Node.js 18.x
- **Memory**: 1024 MB
- **Timeout**: 15 minutes
- **Dependencies**: yt-dlp, FFmpeg, AWS SDK

## 📦 Services

### YouTubeService
- Video information extraction
- Video downloading using yt-dlp
- URL validation and video ID extraction

### AudioService
- Audio conversion using FFmpeg
- Bitrate and speed adjustment
- Audio file validation

### S3Service
- File upload to AWS S3
- Presigned URL generation
- File management and cleanup

### ConversionService
- Orchestrates the entire conversion process
- Handles async processing
- Progress tracking and error handling

## 🚀 Deployment

### Prerequisites
- AWS CLI configured
- Node.js 18+ installed
- Serverless Framework installed globally

### Install Dependencies
```bash
cd lambda
npm install
```

### Build
```bash
npm run build
```

### Deploy with Serverless
```bash
# Install Serverless Framework
npm install -g serverless

# Deploy to AWS
serverless deploy --stage prod --region us-east-1
```

### Manual Deployment
```bash
# Build and package
npm run package

# Deploy using AWS CLI
aws lambda update-function-code \
  --function-name youtube-mp3-converter \
  --zip-file fileb://function.zip
```

## 🔧 Configuration

### Environment Variables
```bash
AWS_S3_BUCKET=your-s3-bucket-name
AWS_S3_REGION=us-east-1
LOG_LEVEL=1
NODE_ENV=production
```

### Required AWS Permissions
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

## 📡 API Endpoints

### POST /convert
Start a new conversion
```json
{
  "youtubeUrl": "https://youtube.com/watch?v=...",
  "bitrate": 192,
  "durationFactor": 1.0,
  "userId": "optional-user-id"
}
```

### POST /convert/info
Get video information without converting
```json
{
  "youtubeUrl": "https://youtube.com/watch?v=..."
}
```

### GET /convert/status?id=conversion-id
Get conversion status

### GET /convert/options
Get supported conversion options

### GET /health
Health check endpoint

## 🧪 Testing

### Local Testing
```bash
# Run local tests
npm run dev

# Test with sample data
npm test
```

### Test Individual Services
```bash
# Test YouTube service
ts-node test/local-test.ts
```

## 📊 Monitoring

### CloudWatch Logs
- Function logs are automatically sent to CloudWatch
- Log level can be controlled via LOG_LEVEL environment variable

### Metrics
- Execution duration
- Memory usage
- Error rates
- Invocation count

## 🔍 Troubleshooting

### Common Issues

**yt-dlp not found**
- Ensure yt-dlp is included in the deployment package
- Check Lambda layer configuration

**FFmpeg not found**
- Use the FFmpeg Lambda layer
- Verify FFMPEG_PATH environment variable

**S3 permissions**
- Check IAM role permissions
- Verify bucket policy

**Timeout errors**
- Increase Lambda timeout (max 15 minutes)
- Optimize video download and conversion

### Debug Mode
Set `LOG_LEVEL=0` for detailed debug logs.

## 🔒 Security

### Best Practices
- Use IAM roles with minimal permissions
- Enable S3 bucket encryption
- Set appropriate CORS policies
- Validate all input parameters

### Input Validation
- YouTube URL format validation
- Bitrate and duration factor limits
- File size restrictions

## 📈 Performance

### Optimization Tips
- Use appropriate Lambda memory allocation
- Implement connection pooling for S3
- Cache video information when possible
- Clean up temporary files promptly

### Scaling
- Lambda automatically scales based on demand
- Consider using SQS for queue management
- Implement rate limiting for API endpoints

## 🔄 Updates

### Updating Dependencies
```bash
npm update
npm audit fix
```

### Updating Lambda Function
```bash
npm run build
npm run package
serverless deploy
```

## 📋 TODO

- [ ] Implement database integration
- [ ] Add WebSocket support for real-time progress
- [ ] Implement conversion queue management
- [ ] Add support for batch conversions
- [ ] Implement user authentication
- [ ] Add conversion history tracking
