{"name": "youtube-mp3-converter-lambda", "version": "1.0.0", "description": "AWS Lambda function for YouTube to MP3 conversion", "main": "index.js", "scripts": {"build": "tsc", "package": "npm run build && zip -r function.zip dist/ node_modules/", "deploy": "aws lambda update-function-code --function-name youtube-mp3-converter --zip-file fileb://function.zip", "test": "jest", "dev": "ts-node src/index.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.490.0", "@aws-sdk/s3-request-presigner": "^3.490.0", "aws-lambda": "^1.0.7", "fluent-ffmpeg": "^2.1.2", "node-fetch": "^3.3.2", "uuid": "^9.0.1", "yt-dlp-wrap": "^2.11.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.131", "@types/fluent-ffmpeg": "^2.1.24", "@types/node": "^20.10.6", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "keywords": ["youtube", "mp3", "converter", "aws", "lambda", "ffmpeg"], "author": "YouTube MP3 Converter", "license": "MIT"}