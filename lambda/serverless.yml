service: youtube-mp3-converter

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region, 'us-east-1'}
  stage: ${opt:stage, 'dev'}
  memorySize: 1024
  timeout: 900 # 15 minutes
  
  environment:
    AWS_S3_BUCKET: ${self:custom.s3BucketName}
    AWS_S3_REGION: ${self:provider.region}
    LOG_LEVEL: ${opt:logLevel, '1'}
    NODE_ENV: ${opt:stage, 'dev'}
  
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
            - s3:ListBucket
          Resource:
            - arn:aws:s3:::${self:custom.s3BucketName}
            - arn:aws:s3:::${self:custom.s3BucketName}/*
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:*:*:*

functions:
  convert:
    handler: dist/index.lambdaHandler
    events:
      - http:
          path: /convert
          method: post
          cors: true
      - http:
          path: /convert/info
          method: post
          cors: true
      - http:
          path: /convert/status
          method: get
          cors: true
      - http:
          path: /convert/options
          method: get
          cors: true
      - http:
          path: /health
          method: get
          cors: true
    layers:
      - ${self:custom.ffmpegLayer}

resources:
  Resources:
    S3Bucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.s3BucketName}
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders:
                - "*"
              AllowedMethods:
                - GET
                - PUT
                - POST
                - DELETE
              AllowedOrigins:
                - "*"
              MaxAge: 3000
        LifecycleConfiguration:
          Rules:
            - Id: DeleteOldFiles
              Status: Enabled
              ExpirationInDays: 7 # Delete files after 7 days

custom:
  s3BucketName: youtube-mp3-converter-${self:provider.stage}-${aws:accountId}
  ffmpegLayer: arn:aws:lambda:${self:provider.region}:************:layer:ffmpeg:4

plugins:
  - serverless-plugin-typescript
  - serverless-offline

package:
  exclude:
    - node_modules/**
    - src/**
    - test/**
    - "*.md"
    - .git/**
