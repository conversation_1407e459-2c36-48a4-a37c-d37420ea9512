export const config = {
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    s3: {
      bucket: process.env.AWS_S3_BUCKET || 'youtube-mp3-converter-files',
      region: process.env.AWS_S3_REGION || 'us-east-1',
    }
  },
  
  conversion: {
    maxDuration: 3600, // 1 hour max
    allowedBitrates: [128, 192, 320],
    allowedDurationFactors: [1.0, 1.5, 2.0],
    tempDir: '/tmp',
    maxFileSize: 100 * 1024 * 1024, // 100MB
  },

  youtube: {
    maxRetries: 3,
    timeout: 300000, // 5 minutes
  },

  ffmpeg: {
    timeout: 600000, // 10 minutes
    threads: 2,
  },

  api: {
    corsOrigins: [
      'http://localhost:3000',
      'https://your-domain.vercel.app'
    ]
  }
};

export const validateConfig = () => {
  const required = [
    'AWS_S3_BUCKET'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};
