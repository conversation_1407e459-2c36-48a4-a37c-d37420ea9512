import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ConversionService } from '../services/conversionService';
import { ConversionRequest } from '../types';
import { logger } from '../utils/logger';
import { config } from '../config';

export class ConversionHandler {
  private conversionService: ConversionService;

  constructor() {
    this.conversionService = new ConversionService();
  }

  /**
   * Handle HTTP requests
   */
  async handle(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    logger.info('Handling request', { 
      method: event.httpMethod, 
      path: event.path,
      queryParams: event.queryStringParameters 
    });

    try {
      // CORS headers
      const corsHeaders = {
        'Access-Control-Allow-Origin': this.getAllowedOrigin(event),
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
      };

      // <PERSON>le preflight requests
      if (event.httpMethod === 'OPTIONS') {
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: ''
        };
      }

      // Route requests
      switch (event.httpMethod) {
        case 'POST':
          return await this.handlePost(event, corsHeaders);
        case 'GET':
          return await this.handleGet(event, corsHeaders);
        default:
          return {
            statusCode: 405,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Method not allowed' })
          };
      }
    } catch (error) {
      logger.error('Request handling failed', { error });
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({ 
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      };
    }
  }

  /**
   * Handle POST requests (start conversion)
   */
  private async handlePost(
    event: APIGatewayProxyEvent, 
    headers: Record<string, string>
  ): Promise<APIGatewayProxyResult> {
    try {
      if (!event.body) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Request body is required' })
        };
      }

      const request: ConversionRequest = JSON.parse(event.body);

      // Validate request
      const validation = this.validateConversionRequest(request);
      if (!validation.isValid) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: validation.error })
        };
      }

      // Check if this is a video info request
      if (event.path?.includes('/info')) {
        const videoInfo = await this.conversionService.getVideoInfo(request.youtubeUrl);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ success: true, data: videoInfo })
        };
      }

      // Start conversion
      const result = await this.conversionService.startConversion(request);
      
      return {
        statusCode: result.status === 'failed' ? 400 : 200,
        headers,
        body: JSON.stringify({ success: result.status !== 'failed', data: result })
      };
    } catch (error) {
      logger.error('POST request failed', { error });
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          error: 'Bad request',
          message: error instanceof Error ? error.message : 'Invalid request'
        })
      };
    }
  }

  /**
   * Handle GET requests (status, health check)
   */
  private async handleGet(
    event: APIGatewayProxyEvent,
    headers: Record<string, string>
  ): Promise<APIGatewayProxyResult> {
    try {
      // Health check
      if (event.path?.includes('/health')) {
        const health = await this.conversionService.healthCheck();
        return {
          statusCode: health.status === 'healthy' ? 200 : 503,
          headers,
          body: JSON.stringify({ success: true, data: health })
        };
      }

      // Get supported options
      if (event.path?.includes('/options')) {
        const options = this.conversionService.getSupportedOptions();
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ success: true, data: options })
        };
      }

      // Get conversion status
      const conversionId = event.queryStringParameters?.id;
      if (conversionId) {
        const status = await this.conversionService.getConversionStatus(conversionId);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ success: true, data: status })
        };
      }

      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing conversion ID parameter' })
      };
    } catch (error) {
      logger.error('GET request failed', { error });
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          error: 'Bad request',
          message: error instanceof Error ? error.message : 'Invalid request'
        })
      };
    }
  }

  /**
   * Validate conversion request
   */
  private validateConversionRequest(request: ConversionRequest): { isValid: boolean; error?: string } {
    if (!request.youtubeUrl) {
      return { isValid: false, error: 'YouTube URL is required' };
    }

    if (!request.bitrate || !config.conversion.allowedBitrates.includes(request.bitrate)) {
      return { 
        isValid: false, 
        error: `Invalid bitrate. Allowed values: ${config.conversion.allowedBitrates.join(', ')}` 
      };
    }

    if (!request.durationFactor || !config.conversion.allowedDurationFactors.includes(request.durationFactor)) {
      return { 
        isValid: false, 
        error: `Invalid duration factor. Allowed values: ${config.conversion.allowedDurationFactors.join(', ')}` 
      };
    }

    return { isValid: true };
  }

  /**
   * Get allowed origin for CORS
   */
  private getAllowedOrigin(event: APIGatewayProxyEvent): string {
    const origin = event.headers?.origin || event.headers?.Origin;
    
    if (origin && config.api.corsOrigins.includes(origin)) {
      return origin;
    }
    
    return config.api.corsOrigins[0] || '*';
  }
}
