import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { ConversionHandler } from './handlers/conversionHandler';
import { validateConfig } from './config';
import { logger } from './utils/logger';

// Initialize handler
let handler: ConversionHandler;

/**
 * Lambda function entry point
 */
export const lambdaHandler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  // Set Lambda context
  context.callbackWaitsForEmptyEventLoop = false;

  try {
    // Validate configuration on cold start
    if (!handler) {
      logger.info('Cold start - initializing handler');
      validateConfig();
      handler = new ConversionHandler();
      logger.info('Handler initialized successfully');
    }

    // Log request details
    logger.info('Processing request', {
      requestId: context.awsRequestId,
      method: event.httpMethod,
      path: event.path,
      userAgent: event.headers?.['User-Agent'],
      sourceIp: event.requestContext?.identity?.sourceIp
    });

    // Process request
    const result = await handler.handle(event);

    // Log response
    logger.info('Request processed successfully', {
      requestId: context.awsRequestId,
      statusCode: result.statusCode,
      responseSize: result.body?.length || 0
    });

    return result;
  } catch (error) {
    logger.error('Lambda handler failed', {
      requestId: context.awsRequestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        error: 'Internal server error',
        requestId: context.awsRequestId
      })
    };
  }
};

// Export for local testing
export { ConversionHandler } from './handlers/conversionHandler';
export { ConversionService } from './services/conversionService';
export * from './types';
