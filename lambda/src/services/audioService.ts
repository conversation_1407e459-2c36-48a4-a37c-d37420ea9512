import ffmpeg from 'fluent-ffmpeg';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface AudioConversionOptions {
  bitrate: number;
  durationFactor: number;
  outputFormat: string;
}

export class AudioService {
  private tempDir: string;

  constructor() {
    this.tempDir = config.conversion.tempDir;
    
    // Set FFmpeg path if available
    if (process.env.FFMPEG_PATH) {
      ffmpeg.setFfmpegPath(process.env.FFMPEG_PATH);
    }
    if (process.env.FFPROBE_PATH) {
      ffmpeg.setFfprobePath(process.env.FFPROBE_PATH);
    }
  }

  /**
   * Convert audio file with specified options
   */
  async convertAudio(
    inputPath: string,
    options: AudioConversionOptions
  ): Promise<string> {
    logger.info('Starting audio conversion', { inputPath, options });

    // Validate bitrate
    if (!config.conversion.allowedBitrates.includes(options.bitrate)) {
      throw new Error(`Invalid bitrate: ${options.bitrate}. Allowed: ${config.conversion.allowedBitrates.join(', ')}`);
    }

    // Validate duration factor
    if (!config.conversion.allowedDurationFactors.includes(options.durationFactor)) {
      throw new Error(`Invalid duration factor: ${options.durationFactor}. Allowed: ${config.conversion.allowedDurationFactors.join(', ')}`);
    }

    const outputFileName = `converted_${uuidv4()}.${options.outputFormat}`;
    const outputPath = path.join(this.tempDir, outputFileName);

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath)
        .audioBitrate(options.bitrate)
        .audioCodec('libmp3lame')
        .format(options.outputFormat)
        .output(outputPath);

      // Apply speed/duration factor if not 1.0
      if (options.durationFactor !== 1.0) {
        command = command.audioFilters(`atempo=${options.durationFactor}`);
      }

      // Set additional options
      command = command
        .audioChannels(2) // Stereo
        .audioFrequency(44100) // 44.1kHz
        .outputOptions([
          '-threads', config.ffmpeg.threads.toString(),
          '-avoid_negative_ts', 'make_zero'
        ]);

      // Progress tracking
      command.on('progress', (progress) => {
        logger.debug('Conversion progress', { 
          percent: progress.percent,
          currentKbps: progress.currentKbps,
          targetSize: progress.targetSize
        });
      });

      // Error handling
      command.on('error', (error) => {
        logger.error('FFmpeg conversion failed', { error: error.message });
        reject(new Error(`Audio conversion failed: ${error.message}`));
      });

      // Success handling
      command.on('end', async () => {
        try {
          // Verify output file exists and has content
          const stats = await fs.stat(outputPath);
          if (stats.size === 0) {
            throw new Error('Output file is empty');
          }

          // Check file size limit
          if (stats.size > config.conversion.maxFileSize) {
            throw new Error(`Output file too large: ${stats.size} bytes (max: ${config.conversion.maxFileSize})`);
          }

          logger.info('Audio conversion completed successfully', { 
            outputPath, 
            fileSize: stats.size 
          });
          resolve(outputPath);
        } catch (error) {
          logger.error('Error verifying output file', { error });
          reject(new Error('Failed to verify converted file'));
        }
      });

      // Start conversion with timeout
      command.run();

      // Set timeout
      setTimeout(() => {
        command.kill('SIGKILL');
        reject(new Error('Audio conversion timeout'));
      }, config.ffmpeg.timeout);
    });
  }

  /**
   * Get audio file metadata
   */
  async getAudioMetadata(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (error, metadata) => {
        if (error) {
          logger.error('Failed to get audio metadata', { error });
          reject(error);
        } else {
          logger.debug('Audio metadata extracted', { metadata });
          resolve(metadata);
        }
      });
    });
  }

  /**
   * Validate audio file
   */
  async validateAudioFile(filePath: string): Promise<boolean> {
    try {
      const metadata = await this.getAudioMetadata(filePath);
      
      // Check if file has audio streams
      const audioStreams = metadata.streams?.filter((stream: any) => stream.codec_type === 'audio');
      
      if (!audioStreams || audioStreams.length === 0) {
        logger.error('No audio streams found in file', { filePath });
        return false;
      }

      // Check duration
      const duration = metadata.format?.duration;
      if (!duration || duration <= 0) {
        logger.error('Invalid audio duration', { filePath, duration });
        return false;
      }

      logger.info('Audio file validation passed', { 
        filePath, 
        duration, 
        audioStreams: audioStreams.length 
      });
      return true;
    } catch (error) {
      logger.error('Audio file validation failed', { filePath, error });
      return false;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanup(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
      logger.info('Audio file cleaned up', { filePath });
    } catch (error) {
      logger.warn('Failed to clean up audio file', { filePath, error });
    }
  }

  /**
   * Get supported audio formats
   */
  getSupportedFormats(): string[] {
    return ['mp3', 'wav', 'aac', 'm4a'];
  }

  /**
   * Get supported bitrates
   */
  getSupportedBitrates(): number[] {
    return config.conversion.allowedBitrates;
  }

  /**
   * Get supported duration factors
   */
  getSupportedDurationFactors(): number[] {
    return config.conversion.allowedDurationFactors;
  }
}
