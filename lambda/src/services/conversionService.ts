import { v4 as uuidv4 } from 'uuid';
import { YouTubeService } from './youtubeService';
import { AudioService } from './audioService';
import { S3Service } from './s3Service';
import { ConversionRequest, ConversionResponse, ConversionProgress, VideoInfo } from '../types';
import { logger } from '../utils/logger';

export class ConversionService {
  private youtubeService: YouTubeService;
  private audioService: AudioService;
  private s3Service: S3Service;

  constructor() {
    this.youtubeService = new YouTubeService();
    this.audioService = new AudioService();
    this.s3Service = new S3Service();
  }

  /**
   * Start conversion process
   */
  async startConversion(request: ConversionRequest): Promise<ConversionResponse> {
    const conversionId = uuidv4();
    logger.info('Starting conversion process', { conversionId, request });

    try {
      // Validate YouTube URL
      if (!this.youtubeService.isValidYouTubeUrl(request.youtubeUrl)) {
        throw new Error('Invalid YouTube URL format');
      }

      // Extract video ID
      const videoId = this.youtubeService.extractVideoId(request.youtubeUrl);
      if (!videoId) {
        throw new Error('Could not extract video ID from URL');
      }

      // Return immediate response with conversion ID
      const response: ConversionResponse = {
        conversionId,
        status: 'pending',
        message: 'Conversion started successfully'
      };

      // Start async conversion process
      this.processConversion(conversionId, request, videoId).catch(error => {
        logger.error('Conversion process failed', { conversionId, error });
      });

      return response;
    } catch (error) {
      logger.error('Failed to start conversion', { conversionId, error });
      return {
        conversionId,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process conversion asynchronously
   */
  private async processConversion(
    conversionId: string,
    request: ConversionRequest,
    videoId: string
  ): Promise<void> {
    let downloadedFilePath: string | null = null;
    let convertedFilePath: string | null = null;

    try {
      // Step 1: Get video information
      logger.info('Step 1: Getting video information', { conversionId });
      const videoInfo = await this.youtubeService.getVideoInfo(request.youtubeUrl);
      
      // Step 2: Download video
      logger.info('Step 2: Downloading video', { conversionId });
      downloadedFilePath = await this.youtubeService.downloadVideo(request.youtubeUrl, videoId);

      // Step 3: Convert audio
      logger.info('Step 3: Converting audio', { conversionId });
      convertedFilePath = await this.audioService.convertAudio(downloadedFilePath, {
        bitrate: request.bitrate,
        durationFactor: request.durationFactor,
        outputFormat: 'mp3'
      });

      // Step 4: Validate converted file
      logger.info('Step 4: Validating converted file', { conversionId });
      const isValid = await this.audioService.validateAudioFile(convertedFilePath);
      if (!isValid) {
        throw new Error('Converted audio file validation failed');
      }

      // Step 5: Upload to S3
      logger.info('Step 5: Uploading to S3', { conversionId });
      const fileName = `${videoInfo.title.replace(/[^a-zA-Z0-9]/g, '_')}_${videoId}.mp3`;
      const uploadResult = await this.s3Service.uploadFile(
        convertedFilePath,
        fileName,
        'audio/mpeg'
      );

      // Step 6: Generate download URL
      logger.info('Step 6: Generating download URL', { conversionId });
      const downloadUrl = await this.s3Service.generateDownloadUrl(uploadResult.key, 86400); // 24 hours

      logger.info('Conversion completed successfully', { 
        conversionId, 
        downloadUrl,
        uploadResult 
      });

      // TODO: Update database with completion status
      // await this.updateConversionStatus(conversionId, 'completed', downloadUrl);

      // TODO: Send callback notification if provided
      // if (request.callbackUrl) {
      //   await this.sendCallback(request.callbackUrl, conversionId, 'completed', downloadUrl);
      // }

    } catch (error) {
      logger.error('Conversion process failed', { conversionId, error });
      
      // TODO: Update database with failure status
      // await this.updateConversionStatus(conversionId, 'failed', undefined, error.message);

      // TODO: Send callback notification if provided
      // if (request.callbackUrl) {
      //   await this.sendCallback(request.callbackUrl, conversionId, 'failed', undefined, error.message);
      // }
    } finally {
      // Cleanup temporary files
      if (downloadedFilePath) {
        await this.youtubeService.cleanup(downloadedFilePath);
      }
      if (convertedFilePath) {
        await this.audioService.cleanup(convertedFilePath);
      }
    }
  }

  /**
   * Get conversion status
   */
  async getConversionStatus(conversionId: string): Promise<ConversionProgress> {
    logger.info('Getting conversion status', { conversionId });

    // TODO: Implement database lookup
    // For now, return mock data
    return {
      conversionId,
      status: 'processing',
      progress: 50,
      currentStep: 'Converting audio',
      estimatedTimeRemaining: 60
    };
  }

  /**
   * Cancel conversion
   */
  async cancelConversion(conversionId: string): Promise<boolean> {
    logger.info('Cancelling conversion', { conversionId });

    try {
      // TODO: Implement conversion cancellation logic
      // This would involve:
      // 1. Updating database status to 'cancelled'
      // 2. Killing any running processes
      // 3. Cleaning up temporary files
      
      return true;
    } catch (error) {
      logger.error('Failed to cancel conversion', { conversionId, error });
      return false;
    }
  }

  /**
   * Get video information without starting conversion
   */
  async getVideoInfo(youtubeUrl: string): Promise<VideoInfo> {
    logger.info('Getting video info only', { youtubeUrl });

    if (!this.youtubeService.isValidYouTubeUrl(youtubeUrl)) {
      throw new Error('Invalid YouTube URL format');
    }

    return await this.youtubeService.getVideoInfo(youtubeUrl);
  }

  /**
   * Get supported conversion options
   */
  getSupportedOptions() {
    return {
      bitrates: this.audioService.getSupportedBitrates(),
      durationFactors: this.audioService.getSupportedDurationFactors(),
      formats: this.audioService.getSupportedFormats()
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: string; services: any }> {
    const services = {
      youtube: 'unknown',
      audio: 'unknown',
      s3: 'unknown'
    };

    try {
      // Test YouTube service
      services.youtube = this.youtubeService.isValidYouTubeUrl('https://youtube.com/watch?v=test') ? 'ok' : 'error';
    } catch {
      services.youtube = 'error';
    }

    try {
      // Test audio service
      const formats = this.audioService.getSupportedFormats();
      services.audio = formats.length > 0 ? 'ok' : 'error';
    } catch {
      services.audio = 'error';
    }

    try {
      // Test S3 service
      const bucket = this.s3Service.getBucket();
      services.s3 = bucket ? 'ok' : 'error';
    } catch {
      services.s3 = 'error';
    }

    const allOk = Object.values(services).every(status => status === 'ok');
    
    return {
      status: allOk ? 'healthy' : 'degraded',
      services
    };
  }
}
