import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../config';
import { logger } from '../utils/logger';
import { S3UploadResult } from '../types';

export class S3Service {
  private s3Client: S3Client;
  private bucket: string;

  constructor() {
    this.s3Client = new S3Client({
      region: config.aws.s3.region,
    });
    this.bucket = config.aws.s3.bucket;
  }

  /**
   * Upload file to S3
   */
  async uploadFile(
    filePath: string,
    fileName: string,
    contentType: string = 'audio/mpeg'
  ): Promise<S3UploadResult> {
    logger.info('Starting S3 upload', { filePath, fileName, contentType });

    try {
      // Read file
      const fileContent = await fs.readFile(filePath);
      
      // Generate unique key
      const key = `audio/${new Date().getFullYear()}/${new Date().getMonth() + 1}/${uuidv4()}_${fileName}`;

      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: fileContent,
        ContentType: contentType,
        Metadata: {
          'original-filename': fileName,
          'upload-timestamp': new Date().toISOString(),
        },
        // Set expiration for temporary files (optional)
        // Expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      });

      const response = await this.s3Client.send(command);

      const result: S3UploadResult = {
        bucket: this.bucket,
        key: key,
        location: `https://${this.bucket}.s3.${config.aws.s3.region}.amazonaws.com/${key}`,
        etag: response.ETag || '',
      };

      logger.info('S3 upload completed successfully', { result });
      return result;
    } catch (error) {
      logger.error('S3 upload failed', { error, filePath, fileName });
      throw new Error(`Failed to upload file to S3: ${error}`);
    }
  }

  /**
   * Generate presigned download URL
   */
  async generateDownloadUrl(
    key: string,
    expiresIn: number = 3600 // 1 hour default
  ): Promise<string> {
    logger.info('Generating presigned download URL', { key, expiresIn });

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      
      logger.info('Presigned URL generated successfully', { key, url });
      return url;
    } catch (error) {
      logger.error('Failed to generate presigned URL', { error, key });
      throw new Error(`Failed to generate download URL: ${error}`);
    }
  }

  /**
   * Delete file from S3
   */
  async deleteFile(key: string): Promise<void> {
    logger.info('Deleting file from S3', { key });

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.s3Client.send(command);
      logger.info('File deleted successfully from S3', { key });
    } catch (error) {
      logger.error('Failed to delete file from S3', { error, key });
      throw new Error(`Failed to delete file from S3: ${error}`);
    }
  }

  /**
   * Check if file exists in S3
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file metadata from S3
   */
  async getFileMetadata(key: string): Promise<any> {
    logger.info('Getting file metadata from S3', { key });

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      const metadata = {
        contentType: response.ContentType,
        contentLength: response.ContentLength,
        lastModified: response.LastModified,
        etag: response.ETag,
        metadata: response.Metadata,
      };

      logger.info('File metadata retrieved successfully', { key, metadata });
      return metadata;
    } catch (error) {
      logger.error('Failed to get file metadata from S3', { error, key });
      throw new Error(`Failed to get file metadata: ${error}`);
    }
  }

  /**
   * Generate upload presigned URL (for direct client uploads)
   */
  async generateUploadUrl(
    fileName: string,
    contentType: string = 'audio/mpeg',
    expiresIn: number = 3600
  ): Promise<{ uploadUrl: string; key: string }> {
    logger.info('Generating presigned upload URL', { fileName, contentType, expiresIn });

    try {
      const key = `uploads/${uuidv4()}_${fileName}`;
      
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        ContentType: contentType,
      });

      const uploadUrl = await getSignedUrl(this.s3Client, command, { expiresIn });
      
      logger.info('Presigned upload URL generated successfully', { key, uploadUrl });
      return { uploadUrl, key };
    } catch (error) {
      logger.error('Failed to generate presigned upload URL', { error, fileName });
      throw new Error(`Failed to generate upload URL: ${error}`);
    }
  }

  /**
   * List files in bucket with prefix
   */
  async listFiles(prefix: string = 'audio/', maxKeys: number = 100): Promise<any[]> {
    logger.info('Listing files in S3', { prefix, maxKeys });

    try {
      const { ListObjectsV2Command } = await import('@aws-sdk/client-s3');
      
      const command = new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: prefix,
        MaxKeys: maxKeys,
      });

      const response = await this.s3Client.send(command);
      const files = response.Contents || [];

      logger.info('Files listed successfully', { count: files.length });
      return files;
    } catch (error) {
      logger.error('Failed to list files in S3', { error, prefix });
      throw new Error(`Failed to list files: ${error}`);
    }
  }

  /**
   * Get bucket name
   */
  getBucket(): string {
    return this.bucket;
  }

  /**
   * Get S3 region
   */
  getRegion(): string {
    return config.aws.s3.region;
  }
}
