import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { VideoInfo } from '../types';
import { config } from '../config';
import { logger } from '../utils/logger';

export class YouTubeService {
  private tempDir: string;

  constructor() {
    this.tempDir = config.conversion.tempDir;
  }

  /**
   * Extract video information from YouTube URL
   */
  async getVideoInfo(youtubeUrl: string): Promise<VideoInfo> {
    logger.info('Extracting video info', { youtubeUrl });

    return new Promise((resolve, reject) => {
      const ytDlp = spawn('yt-dlp', [
        '--dump-json',
        '--no-download',
        youtubeUrl
      ]);

      let output = '';
      let error = '';

      ytDlp.stdout.on('data', (data) => {
        output += data.toString();
      });

      ytDlp.stderr.on('data', (data) => {
        error += data.toString();
      });

      ytDlp.on('close', (code) => {
        if (code === 0) {
          try {
            const videoData = JSON.parse(output);
            const videoInfo: VideoInfo = {
              id: videoData.id,
              title: videoData.title || 'Unknown Title',
              duration: videoData.duration || 0,
              thumbnail: videoData.thumbnail || '',
              uploader: videoData.uploader || 'Unknown',
              uploadDate: videoData.upload_date || ''
            };

            // Validate duration
            if (videoInfo.duration > config.conversion.maxDuration) {
              reject(new Error(`Video too long: ${videoInfo.duration}s (max: ${config.conversion.maxDuration}s)`));
              return;
            }

            logger.info('Video info extracted successfully', { videoInfo });
            resolve(videoInfo);
          } catch (parseError) {
            logger.error('Failed to parse video info', { error: parseError, output });
            reject(new Error('Failed to parse video information'));
          }
        } else {
          logger.error('yt-dlp failed', { code, error });
          reject(new Error(`Failed to extract video info: ${error}`));
        }
      });

      // Set timeout
      setTimeout(() => {
        ytDlp.kill();
        reject(new Error('Video info extraction timeout'));
      }, config.youtube.timeout);
    });
  }

  /**
   * Download video from YouTube
   */
  async downloadVideo(youtubeUrl: string, videoId: string): Promise<string> {
    logger.info('Starting video download', { youtubeUrl, videoId });

    const outputPath = path.join(this.tempDir, `${videoId}_${uuidv4()}.%(ext)s`);

    return new Promise((resolve, reject) => {
      const ytDlp = spawn('yt-dlp', [
        '--format', 'bestaudio/best',
        '--extract-audio',
        '--audio-format', 'mp3',
        '--output', outputPath,
        youtubeUrl
      ]);

      let error = '';

      ytDlp.stderr.on('data', (data) => {
        const message = data.toString();
        error += message;
        logger.debug('yt-dlp output', { message });
      });

      ytDlp.on('close', async (code) => {
        if (code === 0) {
          try {
            // Find the downloaded file
            const files = await fs.readdir(this.tempDir);
            const downloadedFile = files.find(file => 
              file.includes(videoId) && file.endsWith('.mp3')
            );

            if (downloadedFile) {
              const filePath = path.join(this.tempDir, downloadedFile);
              logger.info('Video downloaded successfully', { filePath });
              resolve(filePath);
            } else {
              reject(new Error('Downloaded file not found'));
            }
          } catch (fsError) {
            logger.error('Error finding downloaded file', { error: fsError });
            reject(new Error('Failed to locate downloaded file'));
          }
        } else {
          logger.error('yt-dlp download failed', { code, error });
          reject(new Error(`Download failed: ${error}`));
        }
      });

      // Set timeout
      setTimeout(() => {
        ytDlp.kill();
        reject(new Error('Download timeout'));
      }, config.youtube.timeout);
    });
  }

  /**
   * Validate YouTube URL format
   */
  isValidYouTubeUrl(url: string): boolean {
    const youtubePatterns = [
      /^(https?:\/\/)?(www\.)?youtube\.com\/watch\?v=[\w-]+/,
      /^(https?:\/\/)?(www\.)?youtube\.com\/shorts\/[\w-]+/,
      /^(https?:\/\/)?(www\.)?youtube\.com\/embed\/[\w-]+/,
      /^(https?:\/\/)?youtu\.be\/[\w-]+/,
    ];
    
    return youtubePatterns.some(pattern => pattern.test(url));
  }

  /**
   * Extract video ID from YouTube URL
   */
  extractVideoId(url: string): string | null {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtube\.com\/embed\/|youtube\.com\/v\/|youtube\.com\/shorts\/|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
      /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    
    return null;
  }

  /**
   * Clean up temporary files
   */
  async cleanup(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
      logger.info('Temporary file cleaned up', { filePath });
    } catch (error) {
      logger.warn('Failed to clean up temporary file', { filePath, error });
    }
  }
}
