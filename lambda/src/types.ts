export interface ConversionRequest {
  youtubeUrl: string;
  bitrate: number;
  durationFactor: number;
  userId?: string;
  callbackUrl?: string;
}

export interface ConversionResponse {
  conversionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message?: string;
  downloadUrl?: string;
  error?: string;
}

export interface VideoInfo {
  id: string;
  title: string;
  duration: number;
  thumbnail: string;
  uploader: string;
  uploadDate: string;
}

export interface ConversionProgress {
  conversionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  currentStep: string;
  estimatedTimeRemaining?: number;
  error?: string;
}

export interface S3UploadResult {
  bucket: string;
  key: string;
  location: string;
  etag: string;
}

export interface LambdaEvent {
  httpMethod: string;
  path: string;
  queryStringParameters?: { [key: string]: string };
  body?: string;
  headers?: { [key: string]: string };
}

export interface LambdaResponse {
  statusCode: number;
  headers?: { [key: string]: string };
  body: string;
}
