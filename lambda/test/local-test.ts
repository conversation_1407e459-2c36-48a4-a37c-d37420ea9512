import { ConversionService } from '../src/services/conversionService';
import { ConversionRequest } from '../src/types';
import { logger } from '../src/utils/logger';

/**
 * Local testing script for the conversion service
 */
async function testConversion() {
  logger.info('Starting local conversion test');

  const conversionService = new ConversionService();

  // Test data
  const testRequest: ConversionRequest = {
    youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Rick Roll for testing
    bitrate: 192,
    durationFactor: 1.0,
    userId: 'test-user-123'
  };

  try {
    // Test 1: Health check
    logger.info('Test 1: Health check');
    const health = await conversionService.healthCheck();
    logger.info('Health check result', { health });

    // Test 2: Get supported options
    logger.info('Test 2: Get supported options');
    const options = conversionService.getSupportedOptions();
    logger.info('Supported options', { options });

    // Test 3: Get video info
    logger.info('Test 3: Get video info');
    try {
      const videoInfo = await conversionService.getVideoInfo(testRequest.youtubeUrl);
      logger.info('Video info retrieved', { videoInfo });
    } catch (error) {
      logger.warn('Video info test failed (expected in local environment)', { error });
    }

    // Test 4: Start conversion (will fail without proper environment)
    logger.info('Test 4: Start conversion');
    try {
      const result = await conversionService.startConversion(testRequest);
      logger.info('Conversion started', { result });
    } catch (error) {
      logger.warn('Conversion test failed (expected in local environment)', { error });
    }

    logger.info('Local testing completed');
  } catch (error) {
    logger.error('Local testing failed', { error });
  }
}

/**
 * Test individual services
 */
async function testServices() {
  logger.info('Testing individual services');

  // Test YouTube service
  const { YouTubeService } = await import('../src/services/youtubeService');
  const youtubeService = new YouTubeService();

  const testUrls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://www.youtube.com/shorts/Tx387XYIKZg',
    'https://youtu.be/dQw4w9WgXcQ',
    'invalid-url'
  ];

  logger.info('Testing URL validation');
  testUrls.forEach(url => {
    const isValid = youtubeService.isValidYouTubeUrl(url);
    const videoId = youtubeService.extractVideoId(url);
    logger.info('URL test result', { url, isValid, videoId });
  });

  // Test Audio service
  const { AudioService } = await import('../src/services/audioService');
  const audioService = new AudioService();

  logger.info('Testing audio service options');
  logger.info('Supported bitrates', { bitrates: audioService.getSupportedBitrates() });
  logger.info('Supported duration factors', { factors: audioService.getSupportedDurationFactors() });
  logger.info('Supported formats', { formats: audioService.getSupportedFormats() });

  // Test S3 service
  const { S3Service } = await import('../src/services/s3Service');
  const s3Service = new S3Service();

  logger.info('Testing S3 service');
  logger.info('S3 bucket', { bucket: s3Service.getBucket() });
  logger.info('S3 region', { region: s3Service.getRegion() });
}

// Run tests
async function main() {
  try {
    await testServices();
    await testConversion();
  } catch (error) {
    logger.error('Test execution failed', { error });
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}
