[{"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/convert/info/route.ts": "1", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/convert/options/route.ts": "2", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/convert/route.ts": "3", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/health/route.ts": "4", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/popular/route.ts": "5", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/history/page.tsx": "6", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/layout.tsx": "7", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/page.tsx": "8", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/popular/page.tsx": "9", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ApiStatus.tsx": "10", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx": "11", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/Navigation.tsx": "12", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx": "13", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/auth/AuthModal.tsx": "14", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ui/Button.tsx": "15", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ui/Toast.tsx": "16", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/contexts/AuthContext.tsx": "17", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/hooks/useConversion.ts": "18", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/api.ts": "19", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/auth.ts": "20", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/conversionManager.ts": "21", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/database.ts": "22", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/realtime.ts": "23", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/supabase.ts": "24", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/utils.ts": "25", "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/types/index.ts": "26"}, {"size": 2061, "mtime": 1758378228027, "results": "27", "hashOfConfig": "28"}, {"size": 1361, "mtime": 1758378240906, "results": "29", "hashOfConfig": "28"}, {"size": 3849, "mtime": 1758378205501, "results": "30", "hashOfConfig": "28"}, {"size": 2176, "mtime": 1758378259958, "results": "31", "hashOfConfig": "28"}, {"size": 3831, "mtime": 1758336677607, "results": "32", "hashOfConfig": "28"}, {"size": 10400, "mtime": 1758345118066, "results": "33", "hashOfConfig": "28"}, {"size": 1031, "mtime": 1758344996446, "results": "34", "hashOfConfig": "28"}, {"size": 192, "mtime": 1758336320456, "results": "35", "hashOfConfig": "28"}, {"size": 12322, "mtime": 1758345177324, "results": "36", "hashOfConfig": "28"}, {"size": 5735, "mtime": 1758378324487, "results": "37", "hashOfConfig": "28"}, {"size": 8898, "mtime": 1758378156541, "results": "38", "hashOfConfig": "28"}, {"size": 3433, "mtime": 1758378406315, "results": "39", "hashOfConfig": "28"}, {"size": 15950, "mtime": 1758378100758, "results": "40", "hashOfConfig": "28"}, {"size": 10649, "mtime": 1758344929925, "results": "41", "hashOfConfig": "28"}, {"size": 1695, "mtime": 1758336214798, "results": "42", "hashOfConfig": "28"}, {"size": 2452, "mtime": 1758337142481, "results": "43", "hashOfConfig": "28"}, {"size": 3970, "mtime": 1758344862211, "results": "44", "hashOfConfig": "28"}, {"size": 5283, "mtime": 1758378296520, "results": "45", "hashOfConfig": "28"}, {"size": 6922, "mtime": 1758377908676, "results": "46", "hashOfConfig": "28"}, {"size": 4695, "mtime": 1758344844236, "results": "47", "hashOfConfig": "28"}, {"size": 9311, "mtime": 1758377987277, "results": "48", "hashOfConfig": "28"}, {"size": 8034, "mtime": 1758344821339, "results": "49", "hashOfConfig": "28"}, {"size": 7512, "mtime": 1758344893006, "results": "50", "hashOfConfig": "28"}, {"size": 3602, "mtime": 1758344774443, "results": "51", "hashOfConfig": "28"}, {"size": 1405, "mtime": 1758337080332, "results": "52", "hashOfConfig": "28"}, {"size": 1782, "mtime": 1758377947892, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mgo04h", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/convert/info/route.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/convert/options/route.ts", ["132"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/convert/route.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/health/route.ts", ["133"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/popular/route.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/history/page.tsx", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/popular/page.tsx", ["134", "135"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ApiStatus.tsx", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ConversionProgress.tsx", ["136", "137"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/Navigation.tsx", ["138", "139", "140", "141", "142"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx", ["143", "144", "145"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/auth/AuthModal.tsx", ["146"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ui/Button.tsx", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/ui/Toast.tsx", ["147"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/hooks/useConversion.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/api.ts", ["148", "149"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/auth.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/conversionManager.ts", ["150", "151", "152", "153"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/database.ts", ["154", "155"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/realtime.ts", ["156", "157", "158", "159"], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/supabase.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/lib/utils.ts", [], [], "/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/types/index.ts", ["160"], [], {"ruleId": "161", "severity": 1, "message": "162", "line": 5, "column": 27, "nodeType": null, "messageId": "163", "endLine": 5, "endColumn": 34}, {"ruleId": "161", "severity": 1, "message": "162", "line": 5, "column": 27, "nodeType": null, "messageId": "163", "endLine": 5, "endColumn": 34}, {"ruleId": "161", "severity": 1, "message": "164", "line": 5, "column": 32, "nodeType": null, "messageId": "163", "endLine": 5, "endColumn": 36}, {"ruleId": "165", "severity": 2, "message": "166", "line": 188, "column": 20, "nodeType": "167", "messageId": "168", "suggestions": "169"}, {"ruleId": "161", "severity": 1, "message": "170", "line": 31, "column": 3, "nodeType": null, "messageId": "163", "endLine": 31, "endColumn": 11}, {"ruleId": "161", "severity": 1, "message": "171", "line": 171, "column": 29, "nodeType": null, "messageId": "163", "endLine": 171, "endColumn": 34}, {"ruleId": "161", "severity": 1, "message": "172", "line": 6, "column": 42, "nodeType": null, "messageId": "163", "endLine": 6, "endColumn": 46}, {"ruleId": "161", "severity": 1, "message": "173", "line": 6, "column": 48, "nodeType": null, "messageId": "163", "endLine": 6, "endColumn": 54}, {"ruleId": "161", "severity": 1, "message": "174", "line": 14, "column": 10, "nodeType": null, "messageId": "163", "endLine": 14, "endColumn": 23}, {"ruleId": "161", "severity": 1, "message": "175", "line": 18, "column": 9, "nodeType": null, "messageId": "163", "endLine": 18, "endColumn": 24}, {"ruleId": "161", "severity": 1, "message": "176", "line": 19, "column": 9, "nodeType": null, "messageId": "163", "endLine": 19, "endColumn": 13}, {"ruleId": "161", "severity": 1, "message": "177", "line": 51, "column": 16, "nodeType": null, "messageId": "163", "endLine": 51, "endColumn": 21}, {"ruleId": "161", "severity": 1, "message": "178", "line": 118, "column": 10, "nodeType": null, "messageId": "163", "endLine": 118, "endColumn": 16}, {"ruleId": "165", "severity": 2, "message": "166", "line": 410, "column": 54, "nodeType": "167", "messageId": "168", "suggestions": "179"}, {"ruleId": "165", "severity": 2, "message": "166", "line": 234, "column": 22, "nodeType": "167", "messageId": "168", "suggestions": "180"}, {"ruleId": "161", "severity": 1, "message": "181", "line": 3, "column": 10, "nodeType": null, "messageId": "163", "endLine": 3, "endColumn": 18}, {"ruleId": "182", "severity": 2, "message": "183", "line": 124, "column": 60, "nodeType": "184", "messageId": "185", "endLine": 124, "endColumn": 63, "suggestions": "186"}, {"ruleId": "182", "severity": 2, "message": "183", "line": 177, "column": 58, "nodeType": "184", "messageId": "185", "endLine": 177, "endColumn": 61, "suggestions": "187"}, {"ruleId": "161", "severity": 1, "message": "188", "line": 191, "column": 5, "nodeType": null, "messageId": "163", "endLine": 191, "endColumn": 17}, {"ruleId": "161", "severity": 1, "message": "189", "line": 192, "column": 5, "nodeType": null, "messageId": "163", "endLine": 192, "endColumn": 15}, {"ruleId": "161", "severity": 1, "message": "190", "line": 193, "column": 5, "nodeType": null, "messageId": "163", "endLine": 193, "endColumn": 15}, {"ruleId": "161", "severity": 1, "message": "191", "line": 194, "column": 5, "nodeType": null, "messageId": "163", "endLine": 194, "endColumn": 12}, {"ruleId": "182", "severity": 2, "message": "183", "line": 38, "column": 23, "nodeType": "184", "messageId": "185", "endLine": 38, "endColumn": 26, "suggestions": "192"}, {"ruleId": "182", "severity": 2, "message": "183", "line": 247, "column": 37, "nodeType": "184", "messageId": "185", "endLine": 247, "endColumn": 40, "suggestions": "193"}, {"ruleId": "182", "severity": 2, "message": "183", "line": 14, "column": 38, "nodeType": "184", "messageId": "185", "endLine": 14, "endColumn": 41, "suggestions": "194"}, {"ruleId": "182", "severity": 2, "message": "183", "line": 151, "column": 27, "nodeType": "184", "messageId": "185", "endLine": 151, "endColumn": 30, "suggestions": "195"}, {"ruleId": "161", "severity": 1, "message": "196", "line": 168, "column": 16, "nodeType": null, "messageId": "163", "endLine": 168, "endColumn": 23}, {"ruleId": "182", "severity": 2, "message": "183", "line": 243, "column": 37, "nodeType": "184", "messageId": "185", "endLine": 243, "endColumn": 40, "suggestions": "197"}, {"ruleId": "182", "severity": 2, "message": "183", "line": 78, "column": 12, "nodeType": "184", "messageId": "185", "endLine": 78, "endColumn": 15, "suggestions": "198"}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used.", "unusedVar", "'Play' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["199", "200", "201", "202"], "'videoUrl' is defined but never used.", "'index' is defined but never used.", "'User' is defined but never used.", "'LogOut' is defined but never used.", "'showAuthModal' is assigned a value but never used.", "'isAuthenticated' is assigned a value but never used.", "'user' is assigned a value but never used.", "'error' is defined but never used.", "'result' is defined but never used.", ["203", "204", "205", "206"], ["207", "208", "209", "210"], "'useState' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["211", "212"], ["213", "214"], "'conversionId' is defined but never used.", "'onProgress' is defined but never used.", "'onComplete' is defined but never used.", "'onError' is defined but never used.", ["215", "216"], ["217", "218"], ["219", "220"], ["221", "222"], "'payload' is defined but never used.", ["223", "224"], ["225", "226"], {"messageId": "227", "data": "228", "fix": "229", "desc": "230"}, {"messageId": "227", "data": "231", "fix": "232", "desc": "233"}, {"messageId": "227", "data": "234", "fix": "235", "desc": "236"}, {"messageId": "227", "data": "237", "fix": "238", "desc": "239"}, {"messageId": "227", "data": "240", "fix": "241", "desc": "230"}, {"messageId": "227", "data": "242", "fix": "243", "desc": "233"}, {"messageId": "227", "data": "244", "fix": "245", "desc": "236"}, {"messageId": "227", "data": "246", "fix": "247", "desc": "239"}, {"messageId": "227", "data": "248", "fix": "249", "desc": "230"}, {"messageId": "227", "data": "250", "fix": "251", "desc": "233"}, {"messageId": "227", "data": "252", "fix": "253", "desc": "236"}, {"messageId": "227", "data": "254", "fix": "255", "desc": "239"}, {"messageId": "256", "fix": "257", "desc": "258"}, {"messageId": "259", "fix": "260", "desc": "261"}, {"messageId": "256", "fix": "262", "desc": "258"}, {"messageId": "259", "fix": "263", "desc": "261"}, {"messageId": "256", "fix": "264", "desc": "258"}, {"messageId": "259", "fix": "265", "desc": "261"}, {"messageId": "256", "fix": "266", "desc": "258"}, {"messageId": "259", "fix": "267", "desc": "261"}, {"messageId": "256", "fix": "268", "desc": "258"}, {"messageId": "259", "fix": "269", "desc": "261"}, {"messageId": "256", "fix": "270", "desc": "258"}, {"messageId": "259", "fix": "271", "desc": "261"}, {"messageId": "256", "fix": "272", "desc": "258"}, {"messageId": "259", "fix": "273", "desc": "261"}, {"messageId": "256", "fix": "274", "desc": "258"}, {"messageId": "259", "fix": "275", "desc": "261"}, "replaceWithAlt", {"alt": "276"}, {"range": "277", "text": "278"}, "Replace with `&apos;`.", {"alt": "279"}, {"range": "280", "text": "281"}, "Replace with `&lsquo;`.", {"alt": "282"}, {"range": "283", "text": "284"}, "Replace with `&#39;`.", {"alt": "285"}, {"range": "286", "text": "287"}, "Replace with `&rsquo;`.", {"alt": "276"}, {"range": "288", "text": "289"}, {"alt": "279"}, {"range": "290", "text": "291"}, {"alt": "282"}, {"range": "292", "text": "293"}, {"alt": "285"}, {"range": "294", "text": "295"}, {"alt": "276"}, {"range": "296", "text": "297"}, {"alt": "279"}, {"range": "298", "text": "299"}, {"alt": "282"}, {"range": "300", "text": "301"}, {"alt": "285"}, {"range": "302", "text": "303"}, "suggestUnknown", {"range": "304", "text": "305"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "306", "text": "307"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "308", "text": "305"}, {"range": "309", "text": "307"}, {"range": "310", "text": "305"}, {"range": "311", "text": "307"}, {"range": "312", "text": "305"}, {"range": "313", "text": "307"}, {"range": "314", "text": "305"}, {"range": "315", "text": "307"}, {"range": "316", "text": "305"}, {"range": "317", "text": "307"}, {"range": "318", "text": "305"}, {"range": "319", "text": "307"}, {"range": "320", "text": "305"}, {"range": "321", "text": "307"}, "&apos;", [6504, 6546], "\n              Today&apos;s Top 10\n            ", "&lsquo;", [6504, 6546], "\n              Today&lsquo;s Top 10\n            ", "&#39;", [6504, 6546], "\n              Today&#39;s Top 10\n            ", "&rsquo;", [6504, 6546], "\n              Today&rsquo;s Top 10\n            ", [15404, 15450], "Discover today&apos;s most converted YouTube videos", [15404, 15450], "Discover today&lsquo;s most converted YouTube videos", [15404, 15450], "Discover today&#39;s most converted YouTube videos", [15404, 15450], "Discover today&rsquo;s most converted YouTube videos", [8952, 8993], "\n                  Don&apos;t have an account?", [8952, 8993], "\n                  Don&lsquo;t have an account?", [8952, 8993], "\n                  Don&#39;t have an account?", [8952, 8993], "\n                  Don&rsquo;t have an account?", [3684, 3687], "unknown", [3684, 3687], "never", [4989, 4992], [4989, 4992], [1185, 1188], [1185, 1188], [6908, 6911], [6908, 6911], [372, 375], [372, 375], [4565, 4568], [4565, 4568], [6918, 6921], [6918, 6921], [1656, 1659], [1656, 1659]]