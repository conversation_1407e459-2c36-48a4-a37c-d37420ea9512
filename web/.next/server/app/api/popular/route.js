(()=>{var a={};a.id=97,a.ids=[97],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1834:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>B,patchFetch:()=>A,routeModule:()=>w,serverHooks:()=>z,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var d={};c.r(d),c.d(d,{GET:()=>v});var e=c(95736),f=c(9117),g=c(4044),h=c(39326),i=c(32324),j=c(261),k=c(54290),l=c(85328),m=c(38928),n=c(46595),o=c(3421),p=c(17679),q=c(41681),r=c(63446),s=c(86439),t=c(51356),u=c(10641);async function v(){try{let a=[{id:"1",videoTitle:"Top Hit Song 2024 - Official Music Video",youtubeUrl:"https://youtube.com/watch?v=example1",thumbnailUrl:"https://img.youtube.com/vi/example1/maxresdefault.jpg",downloadCount:15420,dailyCount:892,lastUpdated:new Date().toISOString()},{id:"2",videoTitle:"Relaxing Piano Music for Study and Work",youtubeUrl:"https://youtube.com/watch?v=example2",thumbnailUrl:"https://img.youtube.com/vi/example2/maxresdefault.jpg",downloadCount:12350,dailyCount:654,lastUpdated:new Date().toISOString()},{id:"3",videoTitle:"Podcast: Tech Talk Episode 45",youtubeUrl:"https://youtube.com/watch?v=example3",thumbnailUrl:"https://img.youtube.com/vi/example3/maxresdefault.jpg",downloadCount:9876,dailyCount:432,lastUpdated:new Date().toISOString()},{id:"4",videoTitle:"Nature Sounds - Rain and Thunder",youtubeUrl:"https://youtube.com/watch?v=example4",thumbnailUrl:"https://img.youtube.com/vi/example4/maxresdefault.jpg",downloadCount:8765,dailyCount:321,lastUpdated:new Date().toISOString()},{id:"5",videoTitle:"Guitar Tutorial - Beginner Lesson",youtubeUrl:"https://youtube.com/watch?v=example5",thumbnailUrl:"https://img.youtube.com/vi/example5/maxresdefault.jpg",downloadCount:7654,dailyCount:298,lastUpdated:new Date().toISOString()},{id:"6",videoTitle:"Meditation Music - Deep Relaxation",youtubeUrl:"https://youtube.com/watch?v=example6",thumbnailUrl:"https://img.youtube.com/vi/example6/maxresdefault.jpg",downloadCount:6543,dailyCount:267,lastUpdated:new Date().toISOString()},{id:"7",videoTitle:"Comedy Sketch - Funny Moments",youtubeUrl:"https://youtube.com/watch?v=example7",thumbnailUrl:"https://img.youtube.com/vi/example7/maxresdefault.jpg",downloadCount:5432,dailyCount:234,lastUpdated:new Date().toISOString()},{id:"8",videoTitle:"Workout Music - High Energy Mix",youtubeUrl:"https://youtube.com/watch?v=example8",thumbnailUrl:"https://img.youtube.com/vi/example8/maxresdefault.jpg",downloadCount:4321,dailyCount:198,lastUpdated:new Date().toISOString()},{id:"9",videoTitle:"Language Learning - English Conversation",youtubeUrl:"https://youtube.com/watch?v=example9",thumbnailUrl:"https://img.youtube.com/vi/example9/maxresdefault.jpg",downloadCount:3210,dailyCount:156,lastUpdated:new Date().toISOString()},{id:"10",videoTitle:"Cooking Recipe - Easy Pasta Dish",youtubeUrl:"https://youtube.com/watch?v=example10",thumbnailUrl:"https://img.youtube.com/vi/example10/maxresdefault.jpg",downloadCount:2109,dailyCount:123,lastUpdated:new Date().toISOString()}];return u.NextResponse.json({success:!0,data:a,timestamp:new Date().toISOString()})}catch(a){return console.error("Error fetching popular videos:",a),u.NextResponse.json({error:"Failed to fetch popular videos"},{status:500})}}let w=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/popular/route",pathname:"/api/popular",filename:"route",bundlePath:"app/api/popular/route"},distDir:".next",relativeProjectDir:"",resolvedPagePath:"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/api/popular/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:z}=w;function A(){return(0,g.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}async function B(a,b,c){var d;let e="/api/popular/route";"/index"===e&&(e="/");let g=await w.prepare(a,b,{srcPage:e,multiZoneDraftMode:!1});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:x,isDraftMode:y,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!y){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||w.isDev||y||(G="/index"===(G=D)?"/":G);let H=!0===w.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{cacheComponents:!!x.experimental.cacheComponents,authInterrupts:!!x.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=x.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>w.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>w.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await w.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await w.handleResponse({req:a,nextConfig:x,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),y&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await w.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[586,692],()=>b(b.s=1834));module.exports=c})();