(()=>{var a={};a.id=429,a.ids=[429],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32714:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,73524)),"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/history/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/history/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/history/page",pathname:"/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/history/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},33873:a=>{"use strict";a.exports=require("path")},40094:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o});var d=c(21124),e=c(38301),f=c(78711),g=c(75219),h=c(91292),i=c(15303),j=c(12454),k=c(49269),l=c(3991),m=c.n(l),n=c(22397);function o(){let[a,b]=(0,e.useState)([]),[c,l]=(0,e.useState)(!0),[o,p]=(0,e.useState)(null),{user:q,guestId:r,isAuthenticated:s}=(0,n.A)();return(0,d.jsx)("div",{className:"min-h-screen py-8",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,d.jsx)(i.A,{className:"w-10 h-10 text-blue-600 mr-3"}),(0,d.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900",children:"Conversion History"})]}),(0,d.jsx)("p",{className:"text-lg text-gray-600",children:"Track your YouTube to MP3 conversions"})]}),(0,d.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"mb-8",children:(0,d.jsx)(m(),{href:"/",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Converter"})}),(0,d.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white rounded-2xl shadow-lg overflow-hidden",children:c?(0,d.jsxs)("div",{className:"p-12 text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Loading history..."}),(0,d.jsx)("p",{className:"text-gray-500",children:"Please wait while we fetch your conversions."})]}):o?(0,d.jsxs)("div",{className:"p-12 text-center",children:[(0,d.jsx)(h.A,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Error loading history"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:o}),(0,d.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]}):s?0===a.length?(0,d.jsxs)("div",{className:"p-12 text-center",children:[(0,d.jsx)(j.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No conversions yet"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Start converting YouTube videos to see your history here."}),(0,d.jsx)(m(),{href:"/",className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Start Converting"})]}):(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:a.map((a,b)=>(0,d.jsx)(f.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4,delay:.1*b},className:"p-6 hover:bg-gray-50 transition-colors",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(a=>{switch(a){case"completed":return(0,d.jsx)(g.A,{className:"w-5 h-5 text-green-500"});case"processing":return(0,d.jsx)("div",{className:"w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"});case"failed":return(0,d.jsx)(h.A,{className:"w-5 h-5 text-red-500"});default:return(0,d.jsx)(i.A,{className:"w-5 h-5 text-yellow-500"})}})(a.status),(0,d.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-600",children:(a=>{switch(a){case"completed":return"Completed";case"processing":return"Processing";case"failed":return"Failed";default:return"Pending"}})(a.status)}),(0,d.jsx)("span",{className:"ml-4 text-sm text-gray-500",children:(a=>{let b=new Date(a),c=Math.floor((new Date().getTime()-b.getTime())/6e4);return c<60?`${c} minutes ago`:c<1440?`${Math.floor(c/60)} hours ago`:`${Math.floor(c/1440)} days ago`})(a.createdAt)})]}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 truncate",children:a.videoTitle}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600 mb-2",children:[(0,d.jsxs)("span",{children:["Quality: ",a.bitrate,"kbps"]}),(0,d.jsxs)("span",{children:["Speed: ",a.durationFactor,"x"]})]}),(0,d.jsx)("a",{href:a.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800 truncate block",children:a.youtubeUrl})]}),(0,d.jsxs)("div",{className:"ml-4 flex-shrink-0",children:["completed"===a.status&&a.mp3Url&&(0,d.jsxs)("a",{href:a.mp3Url,download:!0,className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Download"]}),"failed"===a.status&&(0,d.jsx)("button",{className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Retry"})]})]})},a.id))}):(0,d.jsxs)("div",{className:"p-12 text-center",children:[(0,d.jsx)(j.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Sign in to view history"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Create an account or sign in to track your conversion history."}),(0,d.jsx)(m(),{href:"/",className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Go to Converter"})]})})]})})}c(56789),c(94609)},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45327:(a,b,c)=>{Promise.resolve().then(c.bind(c,73524))},49269:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},53575:(a,b,c)=>{Promise.resolve().then(c.bind(c,40094))},55591:a=>{"use strict";a.exports=require("https")},56789:(a,b,c)=>{"use strict";c.d(b,{v:()=>f});var d=c(70798);class e{async createConversion(a){let{data:b,error:c}=await d.N.from("conversions").insert({user_id:a.userId,youtube_url:a.youtubeUrl,video_title:a.videoTitle,bitrate:a.bitrate,duration_factor:a.durationFactor,status:a.status||"pending",progress:a.progress||0,current_step:a.currentStep}).select().single();if(c)throw Error(`Failed to create conversion: ${c.message}`);return this.mapConversionFromDb(b)}async updateConversion(a,b){let c={};b.status&&(c.status=b.status),void 0!==b.progress&&(c.progress=b.progress),b.currentStep&&(c.current_step=b.currentStep),b.mp3Url&&(c.mp3_url=b.mp3Url),b.error&&(c.error_message=b.error),b.completedAt&&(c.completed_at=b.completedAt);let{data:e,error:f}=await d.N.from("conversions").update(c).eq("id",a).select().single();if(f)throw Error(`Failed to update conversion: ${f.message}`);return this.mapConversionFromDb(e)}async getConversion(a){let{data:b,error:c}=await d.N.from("conversions").select("*").eq("id",a).single();if(c){if("PGRST116"===c.code)return null;throw Error(`Failed to get conversion: ${c.message}`)}return this.mapConversionFromDb(b)}async getUserConversions(a,b=50){let{data:c,error:e}=await d.N.from("conversions").select("*").eq("user_id",a).order("created_at",{ascending:!1}).limit(b);if(e)throw Error(`Failed to get user conversions: ${e.message}`);return c.map(this.mapConversionFromDb)}async getRecentConversions(a=10){let{data:b,error:c}=await d.N.from("conversions").select("*").order("created_at",{ascending:!1}).limit(a);if(c)throw Error(`Failed to get recent conversions: ${c.message}`);return b.map(this.mapConversionFromDb)}async updatePopularDownload(a,b,c){let{data:e}=await d.N.from("popular_downloads").select("*").eq("youtube_url",a).single();if(e){let{error:a}=await d.N.from("popular_downloads").update({download_count:e.download_count+1,daily_count:e.daily_count+1,last_updated:new Date().toISOString(),video_title:b,thumbnail_url:c||e.thumbnail_url}).eq("id",e.id);if(a)throw Error(`Failed to update popular download: ${a.message}`)}else{let{error:e}=await d.N.from("popular_downloads").insert({youtube_url:a,video_title:b,download_count:1,daily_count:1,thumbnail_url:c,last_updated:new Date().toISOString()});if(e)throw Error(`Failed to create popular download: ${e.message}`)}}async getPopularDownloads(a=10){let{data:b,error:c}=await d.N.from("popular_downloads").select("*").order("daily_count",{ascending:!1}).limit(a);if(c)throw Error(`Failed to get popular downloads: ${c.message}`);return b.map(a=>({id:a.id,videoTitle:a.video_title,youtubeUrl:a.youtube_url,thumbnailUrl:a.thumbnail_url||"",downloadCount:a.download_count,dailyCount:a.daily_count,lastUpdated:a.last_updated}))}async resetDailyCounts(){let{error:a}=await d.N.from("popular_downloads").update({daily_count:0});if(a)throw Error(`Failed to reset daily counts: ${a.message}`)}subscribeToConversion(a,b){return d.N.channel(`conversion:${a}`).on("postgres_changes",{event:"UPDATE",schema:"public",table:"conversions",filter:`id=eq.${a}`},a=>{b(this.mapConversionFromDb(a.new))}).subscribe()}subscribeToUserConversions(a,b){return d.N.channel(`user_conversions:${a}`).on("postgres_changes",{event:"*",schema:"public",table:"conversions",filter:`user_id=eq.${a}`},a=>{a.new&&b(this.mapConversionFromDb(a.new))}).subscribe()}mapConversionFromDb(a){return{id:a.id,userId:a.user_id,videoTitle:a.video_title,youtubeUrl:a.youtube_url,mp3Url:a.mp3_url,status:a.status,bitrate:a.bitrate,durationFactor:a.duration_factor,progress:a.progress||0,currentStep:a.current_step,error:a.error_message,createdAt:a.created_at,completedAt:a.completed_at,fileSize:a.file_size,duration:a.duration}}async cleanupOldConversions(a=30){let b=new Date;b.setDate(b.getDate()-a);let{data:c,error:e}=await d.N.from("conversions").delete().lt("created_at",b.toISOString()).select("id");if(e)throw Error(`Failed to cleanup old conversions: ${e.message}`);return c?.length||0}}let f=new e},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73524:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/history/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/history/page.tsx","default")},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94609:(a,b,c)=>{"use strict";c.d(b,{Z:()=>f});var d=c(70798);class e{subscribeToConversion(a,b,c){this.unsubscribeFromConversion(a);let e=d.N.channel(`conversion_progress:${a}`).on("postgres_changes",{event:"UPDATE",schema:"public",table:"conversions",filter:`id=eq.${a}`},a=>{try{let c=a.new,d={conversionId:c.id,status:c.status,progress:c.progress||0,currentStep:c.current_step||"Starting...",error:c.error_message};b(d)}catch(a){c?.(a)}}).on("presence",{event:"sync"},()=>{console.log("Realtime connection synced for conversion:",a)}).on("presence",{event:"join"},({key:a,newPresences:b})=>{console.log("Joined realtime channel:",a,b)}).on("presence",{event:"leave"},({key:a,leftPresences:b})=>{console.log("Left realtime channel:",a,b)}).subscribe(b=>{"SUBSCRIBED"===b?console.log("Successfully subscribed to conversion updates:",a):"CHANNEL_ERROR"===b&&(console.error("Error subscribing to conversion updates:",a),c?.(Error("Failed to subscribe to realtime updates")))});return this.subscriptions.set(a,e),e}unsubscribeFromConversion(a){let b=this.subscriptions.get(a);b&&(d.N.removeChannel(b),this.subscriptions.delete(a),console.log("Unsubscribed from conversion updates:",a))}subscribeToUserConversions(a,b,c){let e=`user_conversions:${a}`;this.unsubscribeFromUserConversions(a);let f=d.N.channel(e).on("postgres_changes",{event:"*",schema:"public",table:"conversions",filter:`user_id=eq.${a}`},a=>{try{if(a.new){let c=this.mapConversionFromDb(a.new);b(c)}}catch(a){c?.(a)}}).subscribe(b=>{"SUBSCRIBED"===b?console.log("Successfully subscribed to user conversions:",a):"CHANNEL_ERROR"===b&&(console.error("Error subscribing to user conversions:",a),c?.(Error("Failed to subscribe to user conversion updates")))});return this.subscriptions.set(e,f),f}unsubscribeFromUserConversions(a){let b=`user_conversions:${a}`,c=this.subscriptions.get(b);c&&(d.N.removeChannel(c),this.subscriptions.delete(b),console.log("Unsubscribed from user conversions:",a))}subscribeToPopularDownloads(a,b){let c="popular_downloads";this.unsubscribeFromPopularDownloads();let e=d.N.channel(c).on("postgres_changes",{event:"*",schema:"public",table:"popular_downloads"},async c=>{try{let{data:c,error:e}=await d.N.from("popular_downloads").select("*").order("daily_count",{ascending:!1}).limit(10);if(e)return void b?.(Error(e.message));a(c||[])}catch(a){b?.(a)}}).subscribe(a=>{"SUBSCRIBED"===a?console.log("Successfully subscribed to popular downloads"):"CHANNEL_ERROR"===a&&(console.error("Error subscribing to popular downloads"),b?.(Error("Failed to subscribe to popular downloads updates")))});return this.subscriptions.set(c,e),e}unsubscribeFromPopularDownloads(){let a="popular_downloads",b=this.subscriptions.get(a);b&&(d.N.removeChannel(b),this.subscriptions.delete(a),console.log("Unsubscribed from popular downloads"))}unsubscribeAll(){this.subscriptions.forEach((a,b)=>{d.N.removeChannel(a),console.log("Unsubscribed from channel:",b)}),this.subscriptions.clear()}getConnectionStatus(){return d.N.realtime.isConnected()}reconnect(){d.N.realtime.disconnect(),d.N.realtime.connect()}mapConversionFromDb(a){return{id:a.id,userId:a.user_id,videoTitle:a.video_title,youtubeUrl:a.youtube_url,mp3Url:a.mp3_url,status:a.status,bitrate:a.bitrate,durationFactor:a.duration_factor,progress:a.progress||0,currentStep:a.current_step,error:a.error_message,createdAt:a.created_at,completedAt:a.completed_at,fileSize:a.file_size,duration:a.duration}}constructor(){this.subscriptions=new Map}}let f=new e}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[586,379,671],()=>b(b.s=32714));module.exports=c})();