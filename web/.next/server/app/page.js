(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},37029:(a,b,c)=>{"use strict";c.d(b,{default:()=>az});var d=c(21124),e=c(38301),f=c(78711),g=c(12454),h=c(23339);let i=(0,h.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),j=(0,h.A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),k=(0,h.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var l=c(49269),m=c(15303),n=c(67748),o=c(3991),p=c.n(o);let q=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?q(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},r=/^\[(.+)\]$/,s=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:t(b,a)).classGroupId=c;return}if("function"==typeof a)return u(a)?void s(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{s(e,t(b,a),c,d)})})},t=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},u=a=>a.isThemeGetter,v=/\s+/;function w(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=x(a))&&(d&&(d+=" "),d+=b);return d}let x=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=x(a[d]))&&(c&&(c+=" "),c+=b);return c},y=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},z=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,A=/^\((?:(\w[\w-]*):)?(.+)\)$/i,B=/^\d+\/\d+$/,C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,D=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,F=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,G=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,H=a=>B.test(a),I=a=>!!a&&!Number.isNaN(Number(a)),J=a=>!!a&&Number.isInteger(Number(a)),K=a=>a.endsWith("%")&&I(a.slice(0,-1)),L=a=>C.test(a),M=()=>!0,N=a=>D.test(a)&&!E.test(a),O=()=>!1,P=a=>F.test(a),Q=a=>G.test(a),R=a=>!T(a)&&!Z(a),S=a=>ae(a,ai,O),T=a=>z.test(a),U=a=>ae(a,aj,N),V=a=>ae(a,ak,I),W=a=>ae(a,ag,O),X=a=>ae(a,ah,Q),Y=a=>ae(a,am,P),Z=a=>A.test(a),$=a=>af(a,aj),_=a=>af(a,al),aa=a=>af(a,ag),ab=a=>af(a,ai),ac=a=>af(a,ah),ad=a=>af(a,am,!0),ae=(a,b,c)=>{let d=z.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},af=(a,b,c=!1)=>{let d=A.exec(a);return!!d&&(d[1]?b(d[1]):c)},ag=a=>"position"===a||"percentage"===a,ah=a=>"image"===a||"url"===a,ai=a=>"length"===a||"size"===a||"bg-size"===a,aj=a=>"length"===a,ak=a=>"number"===a,al=a=>"family-name"===a,am=a=>"shadow"===a;Symbol.toStringTag;let an=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)s(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),q(c,b)||(a=>{if(r.test(a)){let b=r.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(v),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(w.apply(null,arguments))}}(()=>{let a=y("color"),b=y("font"),c=y("text"),d=y("font-weight"),e=y("tracking"),f=y("leading"),g=y("breakpoint"),h=y("container"),i=y("spacing"),j=y("radius"),k=y("shadow"),l=y("inset-shadow"),m=y("text-shadow"),n=y("drop-shadow"),o=y("blur"),p=y("perspective"),q=y("aspect"),r=y("ease"),s=y("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),Z,T],w=()=>["auto","hidden","clip","visible","scroll"],x=()=>["auto","contain","none"],z=()=>[Z,T,i],A=()=>[H,"full","auto",...z()],B=()=>[J,"none","subgrid",Z,T],C=()=>["auto",{span:["full",J,Z,T]},J,Z,T],D=()=>[J,"auto",Z,T],E=()=>["auto","min","max","fr",Z,T],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],G=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...z()],O=()=>[H,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...z()],P=()=>[a,Z,T],Q=()=>[...u(),aa,W,{position:[Z,T]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",ab,S,{size:[Z,T]}],ag=()=>[K,$,U],ah=()=>["","none","full",j,Z,T],ai=()=>["",I,$,U],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[I,K,aa,W],am=()=>["","none",o,Z,T],an=()=>["none",I,Z,T],ao=()=>["none",I,Z,T],ap=()=>[I,Z,T],aq=()=>[H,"full",...z()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[L],breakpoint:[L],color:[M],container:[L],"drop-shadow":[L],ease:["in","out","in-out"],font:[R],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[L],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[L],shadow:[L],spacing:["px",I],text:[L],"text-shadow":[L],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",H,T,Z,q]}],container:["container"],columns:[{columns:[I,T,Z,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:x()}],"overscroll-x":[{"overscroll-x":x()}],"overscroll-y":[{"overscroll-y":x()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[J,"auto",Z,T]}],basis:[{basis:[H,"full","auto",h,...z()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[I,H,"auto","initial","none",T]}],grow:[{grow:["",I,Z,T]}],shrink:[{shrink:["",I,Z,T]}],order:[{order:[J,"first","last","none",Z,T]}],"grid-cols":[{"grid-cols":B()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":B()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:z()}],"gap-x":[{"gap-x":z()}],"gap-y":[{"gap-y":z()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...G(),"normal"]}],"justify-self":[{"justify-self":["auto",...G()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...G(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...G(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...G(),"baseline"]}],"place-self":[{"place-self":["auto",...G()]}],p:[{p:z()}],px:[{px:z()}],py:[{py:z()}],ps:[{ps:z()}],pe:[{pe:z()}],pt:[{pt:z()}],pr:[{pr:z()}],pb:[{pb:z()}],pl:[{pl:z()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":z()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":z()}],"space-y-reverse":["space-y-reverse"],size:[{size:O()}],w:[{w:[h,"screen",...O()]}],"min-w":[{"min-w":[h,"screen","none",...O()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...O()]}],h:[{h:["screen","lh",...O()]}],"min-h":[{"min-h":["screen","lh","none",...O()]}],"max-h":[{"max-h":["screen","lh",...O()]}],"font-size":[{text:["base",c,$,U]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,Z,V]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",K,T]}],"font-family":[{font:[_,T,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,Z,T]}],"line-clamp":[{"line-clamp":[I,"none",Z,V]}],leading:[{leading:[f,...z()]}],"list-image":[{"list-image":["none",Z,T]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,T]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[I,"from-font","auto",Z,U]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[I,"auto",Z,T]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,T]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,T]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Q()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},J,Z,T],radial:["",Z,T],conic:[J,Z,T]},ac,X]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[I,Z,T]}],"outline-w":[{outline:["",I,$,U]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",k,ad,Y]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",l,ad,Y]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[I,U]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",m,ad,Y]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[I,Z,T]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[I]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[Z,T]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[I]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Q()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Z,T]}],filter:[{filter:["","none",Z,T]}],blur:[{blur:am()}],brightness:[{brightness:[I,Z,T]}],contrast:[{contrast:[I,Z,T]}],"drop-shadow":[{"drop-shadow":["","none",n,ad,Y]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",I,Z,T]}],"hue-rotate":[{"hue-rotate":[I,Z,T]}],invert:[{invert:["",I,Z,T]}],saturate:[{saturate:[I,Z,T]}],sepia:[{sepia:["",I,Z,T]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,T]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[I,Z,T]}],"backdrop-contrast":[{"backdrop-contrast":[I,Z,T]}],"backdrop-grayscale":[{"backdrop-grayscale":["",I,Z,T]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[I,Z,T]}],"backdrop-invert":[{"backdrop-invert":["",I,Z,T]}],"backdrop-opacity":[{"backdrop-opacity":[I,Z,T]}],"backdrop-saturate":[{"backdrop-saturate":[I,Z,T]}],"backdrop-sepia":[{"backdrop-sepia":["",I,Z,T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":z()}],"border-spacing-x":[{"border-spacing-x":z()}],"border-spacing-y":[{"border-spacing-y":z()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,T]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[I,"initial",Z,T]}],ease:[{ease:["linear","initial",r,Z,T]}],delay:[{delay:[I,Z,T]}],animate:[{animate:["none",s,Z,T]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,Z,T]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[Z,T,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,T]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,T]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[I,$,U,V]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),ao=(0,e.forwardRef)(({className:a,variant:b="primary",size:c="md",isLoading:e,children:f,disabled:g,...h},i)=>(0,d.jsx)("button",{ref:i,className:function(...a){return an(function(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}(a))}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border-2 border-red-600 text-red-600 hover:bg-red-600 hover:text-white focus:ring-red-500"}[b],{sm:"px-3 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[c],a),disabled:g||e,...h,children:e?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Converting..."]}):f}));ao.displayName="Button";var ap=c(5227),aq=c(75219),ar=c(18310);let as=(0,h.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function at({message:a,type:b="error",isVisible:c,onClose:e,duration:g=5e3}){return(0,d.jsx)(ap.N,{children:c&&(0,d.jsx)(f.P.div,{initial:{opacity:0,y:-50,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.9},transition:{duration:.3},className:"fixed top-4 right-4 z-50 max-w-md",children:(0,d.jsx)("div",{className:`rounded-lg border p-4 shadow-lg ${(()=>{switch(b){case"success":return"bg-green-50 border-green-200 text-green-800";case"info":return"bg-blue-50 border-blue-200 text-blue-800";default:return"bg-red-50 border-red-200 text-red-800"}})()}`,children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(b){case"success":return(0,d.jsx)(aq.A,{className:"w-5 h-5 text-green-500"});case"info":return(0,d.jsx)(i,{className:"w-5 h-5 text-blue-500"});default:return(0,d.jsx)(ar.A,{className:"w-5 h-5 text-red-500"})}})()}),(0,d.jsx)("div",{className:"ml-3 flex-1",children:(0,d.jsx)("p",{className:"text-sm font-medium whitespace-pre-line",children:a})}),(0,d.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,d.jsx)("button",{onClick:e,className:"inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 transition-colors",children:(0,d.jsx)(as,{className:"w-4 h-4"})})})]})})})})}let au=(0,h.A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]]);var av=c(45478);class aw{constructor(a={}){this.activeConversions=new Map,this.pollIntervals=new Map,this.options={enableDatabase:!1,enableRealtime:!1,pollInterval:2e3,...a}}async startConversion(a,b,c,d){try{let e=await av.K.startConversion(a),f=e.conversionId,g={id:f,userId:a.userId,videoTitle:"Loading...",youtubeUrl:a.youtubeUrl,status:e.status,bitrate:a.bitrate,durationFactor:a.durationFactor,progress:0,currentStep:"Starting conversion...",createdAt:new Date().toISOString()};return this.activeConversions.set(f,g),this.options.enableDatabase,this.options.enableRealtime?this.setupRealtimeUpdates(f,b,c,d):this.setupPolling(f,b,c,d),f}catch(b){let a=b instanceof Error?b.message:"Failed to start conversion";throw d?.(Error(a)),b}}async getConversionStatus(a){let b=this.activeConversions.get(a);if(b)return b;this.options.enableDatabase;try{let b=await av.K.getConversionStatus(a),c={id:a,videoTitle:"Unknown",youtubeUrl:"",status:b.status,bitrate:192,durationFactor:1,progress:b.progress,currentStep:b.currentStep,error:b.error,createdAt:new Date().toISOString()};return this.activeConversions.set(a,c),c}catch(a){return console.error("Failed to get conversion status:",a),null}}async cancelConversion(a){try{let b=await av.K.cancelConversion(a);if(b){let b=this.activeConversions.get(a);b&&(b.status="failed",b.error="Cancelled by user",this.activeConversions.set(a,b)),this.options.enableDatabase,this.stopPolling(a)}return b}catch(a){return console.error("Failed to cancel conversion:",a),!1}}getActiveConversions(){return Array.from(this.activeConversions.values())}cleanupCompletedConversions(){for(let[a,b]of this.activeConversions.entries())("completed"===b.status||"failed"===b.status)&&(this.activeConversions.delete(a),this.stopPolling(a))}setupRealtimeUpdates(a,b,c,d){}setupPolling(a,b,c,d){let e=setInterval(async()=>{try{let e=await av.K.getConversionStatus(a),f=this.activeConversions.get(a);f&&(f.status=e.status,f.progress=e.progress,f.currentStep=e.currentStep,f.error=e.error,"completed"===e.status&&(f.completedAt=new Date().toISOString()),this.activeConversions.set(a,f)),b?.(e),("completed"===e.status||"failed"===e.status)&&(this.stopPolling(a),f&&("completed"===e.status?c?.(f):d?.(Error(e.error||"Conversion failed"))))}catch(b){console.error("Polling error for conversion:",a,b),d?.(b instanceof Error?b:Error("Polling failed")),this.stopPolling(a)}},this.options.pollInterval);this.pollIntervals.set(a,e)}stopPolling(a){let b=this.pollIntervals.get(a);b&&(clearInterval(b),this.pollIntervals.delete(a))}updateConversionProgress(a,b){let c=this.activeConversions.get(a);c&&(c.status=b.status,c.progress=b.progress,c.currentStep=b.currentStep,c.error=b.error,"completed"===b.status&&(c.completedAt=new Date().toISOString()),this.activeConversions.set(a,c))}cleanup(){for(let a of this.pollIntervals.values())clearInterval(a);this.pollIntervals.clear(),this.activeConversions.clear(),this.options.enableRealtime}}let ax=new aw;function ay({isVisible:a,onClose:b,videoUrl:c,conversionId:g,options:h}){let[i,j]=(0,e.useState)([{id:"fetch",label:"Fetching video information",status:"pending"},{id:"download",label:"Downloading video",status:"pending",progress:0},{id:"convert",label:"Converting to MP3",status:"pending",progress:0},{id:"upload",label:"Preparing download",status:"pending",progress:0}]),[k,m]=(0,e.useState)(0),[n,o]=(0,e.useState)(0),[p,q]=(0,e.useState)("Calculating..."),[r,s]=(0,e.useState)(null),[t,u]=(0,e.useState)(null);return a?(0,d.jsx)(f.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)(f.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6",children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Converting Video"}),(0,d.jsxs)("p",{className:"text-gray-600 text-sm",children:["Quality: ",h.bitrate,"kbps • Speed: ",h.durationFactor,"x"]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[Math.round(n),"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,d.jsx)(f.P.div,{className:"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full",initial:{width:0},animate:{width:`${n}%`},transition:{duration:.5}})}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["Estimated time: ",p]})]}),(0,d.jsx)("div",{className:"space-y-4 mb-6",children:i.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(a=>{switch(a.status){case"completed":return(0,d.jsx)(aq.A,{className:"w-5 h-5 text-green-500"});case"processing":return(0,d.jsx)(au,{className:"w-5 h-5 text-blue-500 animate-spin"});case"error":return(0,d.jsx)(ar.A,{className:"w-5 h-5 text-red-500"});default:return(0,d.jsx)("div",{className:"w-5 h-5 rounded-full border-2 border-gray-300"})}})(a),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:`text-sm font-medium ${"completed"===a.status?"text-green-700":"processing"===a.status?"text-blue-700":"error"===a.status?"text-red-700":"text-gray-500"}`,children:a.label}),"processing"===a.status&&void 0!==a.progress&&(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1 mt-1",children:(0,d.jsx)(f.P.div,{className:"bg-blue-500 h-1 rounded-full",initial:{width:0},animate:{width:`${a.progress}%`},transition:{duration:.3}})})]})]},a.id))}),t&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(ar.A,{className:"w-5 h-5 text-red-500 mr-2"}),(0,d.jsx)("p",{className:"text-red-700 text-sm",children:t})]})}),(0,d.jsx)("div",{className:"flex space-x-3",children:r?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("a",{href:r,download:!0,className:"flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center",children:[(0,d.jsx)(l.A,{className:"w-5 h-5 mr-2"}),"Download MP3"]}),(0,d.jsx)("button",{onClick:b,className:"px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Close"})]}):(0,d.jsx)("button",{onClick:b,className:"w-full py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",disabled:!t&&n<100,children:t?"Close":"Cancel"})})]})}):null}function az(){let[a,b]=(0,e.useState)(""),[c,h]=(0,e.useState)({bitrate:192,durationFactor:1}),[o,q]=(0,e.useState)(!1),[r,s]=(0,e.useState)(!1),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)(null),[z,A]=(0,e.useState)(null),[B,C]=(0,e.useState)(!1),[D,E]=(0,e.useState)(null),[F,G]=(0,e.useState)({message:"",type:"error",isVisible:!1}),H=(a,b="error")=>{G({message:a,type:b,isVisible:!0})},I=async()=>{if(![/^(https?:\/\/)?(www\.)?youtube\.com\/watch\?v=[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/shorts\/[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/embed\/[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/v\/[\w-]+/,/^(https?:\/\/)?youtu\.be\/[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/watch\?.*v=[\w-]+/].some(b=>b.test(a)))return void H("Please enter a valid YouTube URL. Supported formats:\n• https://youtube.com/watch?v=...\n• https://youtube.com/shorts/...\n• https://youtu.be/...\n• https://youtube.com/embed/...","error");q(!0),w(!0);try{let b={youtubeUrl:a,bitrate:c.bitrate,durationFactor:c.durationFactor},d=await ax.startConversion(b,a=>{console.log("Conversion progress:",a)},a=>{H("Conversion completed successfully!","success"),q(!1)},a=>{H(`Conversion failed: ${a.message}`,"error"),q(!1)});y(d),H("Conversion started successfully!","success")}catch(a){q(!1),w(!1),H(a instanceof Error?a.message:"Failed to start conversion","error")}},J=D?.bitrates.map(a=>({value:a,label:`${a} kbps ${128===a?"(Good)":192===a?"(High)":"(Best)"}`}))||[{value:128,label:"128 kbps (Good)"},{value:192,label:"192 kbps (High)"},{value:320,label:"320 kbps (Best)"}],K=D?.durationFactors.map(a=>({value:a,label:`${a}x ${1===a?"(Normal)":1.5===a?"(Faster)":"(Fastest)"}`}))||[{value:1,label:"1.0x (Normal)"},{value:1.5,label:"1.5x (Faster)"},{value:2,label:"2.0x (Fastest)"}];return(0,d.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,d.jsx)(g.A,{className:"w-12 h-12 text-red-600 mr-3"}),(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900",children:"YouTube to MP3"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Convert YouTube videos to high-quality MP3 audio files. Fast, free, and easy to use."})]}),(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white rounded-2xl shadow-2xl p-8 mb-8",children:[(0,d.jsxs)("div",{className:"relative mb-4",children:[(0,d.jsx)(f.P.div,{className:`absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-0 transition-opacity duration-300 ${t?"opacity-100":"opacity-0"}`,style:{background:t?"linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ef4444)":"transparent",backgroundSize:"200% 100%",animation:t?"rainbow 2s linear infinite":"none"}}),(0,d.jsx)("div",{className:"relative bg-white rounded-xl p-1",children:(0,d.jsx)("input",{type:"text",value:a,onChange:a=>b(a.target.value),placeholder:"Paste YouTube URL here (videos, shorts, etc.)...",className:"w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-500 focus:outline-none transition-colors"})})]}),(0,d.jsx)("div",{className:"mb-4 text-center",children:(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: Regular videos, YouTube Shorts, youtu.be links, and embed URLs"})}),(B||z)&&(0,d.jsx)(f.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:B?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"}),(0,d.jsx)("span",{className:"text-blue-700",children:"Loading video information..."})]}):z?(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)(i,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"font-semibold text-blue-900 mb-1",children:z.title}),(0,d.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsxs)("p",{children:["Duration: ",Math.floor(z.duration/60),":",(z.duration%60).toString().padStart(2,"0")]}),(0,d.jsxs)("p",{children:["Uploader: ",z.uploader]}),z.uploadDate&&(0,d.jsxs)("p",{children:["Upload Date: ",new Date(z.uploadDate).toLocaleDateString()]})]})]})]}):null}),(0,d.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,d.jsxs)("button",{onClick:()=>s(!r),className:"flex items-center text-gray-600 hover:text-gray-800 transition-colors",children:[(0,d.jsx)(j,{className:"w-5 h-5 mr-2"}),"Conversion Options",(0,d.jsx)(f.P.div,{animate:{rotate:180*!!r},transition:{duration:.2},className:"ml-2",children:"▼"})]})}),(0,d.jsx)(f.P.div,{initial:!1,animate:{height:r?"auto":0,opacity:+!!r},transition:{duration:.3},className:"overflow-hidden",children:(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Audio Quality"}),(0,d.jsx)("select",{value:c.bitrate,onChange:a=>h({...c,bitrate:Number(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none",children:J.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Playback Speed"}),(0,d.jsx)("select",{value:c.durationFactor,onChange:a=>h({...c,durationFactor:Number(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none",children:K.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]})]})}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)(ao,{onClick:I,onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),isLoading:o,size:"lg",className:"px-12 py-4 text-xl",disabled:!a.trim(),children:o?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k,{className:"w-6 h-6 mr-2 animate-pulse"}),"Converting..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.A,{className:"w-6 h-6 mr-2"}),"Convert to MP3"]})})})]}),(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"grid md:grid-cols-3 gap-6 text-center",children:[(0,d.jsxs)("div",{className:"p-6 bg-white rounded-xl shadow-lg",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(k,{className:"w-8 h-8 text-red-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Lightning Fast"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Convert videos in seconds with our optimized servers"})]}),(0,d.jsxs)("div",{className:"p-6 bg-white rounded-xl shadow-lg",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(g.A,{className:"w-8 h-8 text-blue-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"High Quality"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Multiple bitrate options for the best audio quality"})]}),(0,d.jsxs)("div",{className:"p-6 bg-white rounded-xl shadow-lg",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(l.A,{className:"w-8 h-8 text-green-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Free Forever"}),(0,d.jsx)("p",{className:"text-gray-600",children:"No registration required, completely free to use"})]})]}),(0,d.jsxs)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid md:grid-cols-2 gap-6 mt-8",children:[(0,d.jsxs)(p(),{href:"/history",className:"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:(0,d.jsx)(m.A,{className:"w-6 h-6 text-blue-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold ml-4 text-gray-900",children:"View History"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Track your conversion history and re-download files"})]}),(0,d.jsxs)(p(),{href:"/popular",className:"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:(0,d.jsx)(n.A,{className:"w-6 h-6 text-orange-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold ml-4 text-gray-900",children:"Popular Today"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Discover today's most converted YouTube videos"})]})]}),(0,d.jsx)(ay,{isVisible:v,onClose:()=>{w(!1),q(!1),y(null)},videoUrl:a,conversionId:x||void 0,options:c}),(0,d.jsx)(at,{message:F.message,type:F.type,isVisible:F.isVisible,onClose:()=>{G(a=>({...a,isVisible:!1}))}})]})}},37766:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,60967)),"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,87028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,I=["/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/app/page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},49269:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},55591:a=>{"use strict";a.exports=require("https")},60967:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(75338),e=c(91450);function f(){return(0,d.jsx)("div",{className:"min-h-screen py-8",children:(0,d.jsx)(e.default,{})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66892:(a,b,c)=>{Promise.resolve().then(c.bind(c,37029))},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},80044:(a,b,c)=>{Promise.resolve().then(c.bind(c,91450))},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91450:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/YouTubeConverter.tsx","default")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[586,379,671],()=>b(b.s=37766));module.exports=c})();