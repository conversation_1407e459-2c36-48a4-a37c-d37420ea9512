exports.id=671,exports.ids=[671],exports.modules={11172:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(97954);(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/contexts/AuthContext.tsx","useAuth");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/contexts/AuthContext.tsx","AuthProvider")},16778:(a,b,c)=>{Promise.resolve().then(c.bind(c,93855)),Promise.resolve().then(c.bind(c,22397))},22397:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>k,A:()=>j});var d=c(21124),e=c(38301),f=c(70798);class g{async signUp(a,b,c){let{data:d,error:e}=await f.N.auth.signUp({email:a,password:b,options:{data:{display_name:c}}});if(e)throw Error(e.message);return d}async signIn(a,b){let{data:c,error:d}=await f.N.auth.signInWithPassword({email:a,password:b});if(d)throw Error(d.message);return c}async signInWithGoogle(){let{data:a,error:b}=await f.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(b)throw Error(b.message);return a}async signOut(){let{error:a}=await f.N.auth.signOut();if(a)throw Error(a.message)}async getCurrentUser(){let{data:{user:a}}=await f.N.auth.getUser();return a?this.mapUser(a):null}async getCurrentSession(){let{data:{session:a}}=await f.N.auth.getSession();return a}async updateProfile(a){let{data:b,error:c}=await f.N.auth.updateUser({data:{display_name:a.displayName,avatar_url:a.avatarUrl}});if(c)throw Error(c.message);return b}async resetPassword(a){let{error:b}=await f.N.auth.resetPasswordForEmail(a,{redirectTo:`${window.location.origin}/auth/reset-password`});if(b)throw Error(b.message)}async updatePassword(a){let{data:b,error:c}=await f.N.auth.updateUser({password:a});if(c)throw Error(c.message);return b}onAuthStateChange(a){return f.N.auth.onAuthStateChange((b,c)=>{a(c?.user?this.mapUser(c.user):null)})}async isAuthenticated(){return!!await this.getCurrentSession()}async getAccessToken(){let a=await this.getCurrentSession();return a?.access_token||null}async refreshSession(){let{data:a,error:b}=await f.N.auth.refreshSession();if(b)throw Error(b.message);return a}mapUser(a){return{id:a.id,email:a.email||"",displayName:a.user_metadata?.display_name||a.user_metadata?.full_name,avatarUrl:a.user_metadata?.avatar_url,createdAt:a.created_at}}generateGuestId(){let a=localStorage.getItem("guest_id");if(a)return a;let b=`guest_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;return localStorage.setItem("guest_id",b),b}clearGuestSession(){localStorage.removeItem("guest_id")}async isGuestSession(){return!await this.isAuthenticated()&&!!localStorage.getItem("guest_id")}}let h=new g,i=(0,e.createContext)(void 0);function j(){let a=(0,e.useContext)(i);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}function k({children:a}){let[b,c]=(0,e.useState)(null),[f,g]=(0,e.useState)(!0),[j,k]=(0,e.useState)(null),l=async(a,b)=>{g(!0);try{await h.signIn(a,b)}catch(a){throw g(!1),a}},m=async(a,b,c)=>{g(!0);try{await h.signUp(a,b,c)}catch(a){throw g(!1),a}},n=async()=>{g(!0);try{await h.signInWithGoogle()}catch(a){throw g(!1),a}},o=async()=>{g(!0);try{await h.signOut()}catch(a){throw g(!1),a}},p=async a=>{try{await h.updateProfile(a);let b=await h.getCurrentUser();c(b)}catch(a){throw a}},q=async a=>{try{await h.resetPassword(a)}catch(a){throw a}};return(0,d.jsx)(i.Provider,{value:{user:b,loading:f,signIn:l,signUp:m,signInWithGoogle:n,signOut:o,updateProfile:p,resetPassword:q,isAuthenticated:!!b,guestId:j},children:a})}},24169:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/ideal/youtube_mp3/web/src/components/Navigation.tsx","default")},35482:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,81170,23)),Promise.resolve().then(c.t.bind(c,23597,23)),Promise.resolve().then(c.t.bind(c,36893,23)),Promise.resolve().then(c.t.bind(c,89748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,69576,23)),Promise.resolve().then(c.t.bind(c,73041,23)),Promise.resolve().then(c.t.bind(c,51384,23))},45478:(a,b,c)=>{"use strict";c.d(b,{K:()=>e});class d{constructor(){this.baseUrl=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3000/api"}async startConversion(a){try{let b=await fetch(`${this.baseUrl}/convert`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!b.ok){let a=await b.json().catch(()=>({}));throw Error(a.message||`HTTP ${b.status}: ${b.statusText}`)}let c=await b.json();return c.data||c}catch(a){throw console.error("Failed to start conversion:",a),a instanceof Error?a:Error("Failed to start conversion")}}async getVideoInfo(a){try{let b=await fetch(`${this.baseUrl}/convert/info`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeUrl:a})});if(!b.ok){let a=await b.json().catch(()=>({}));throw Error(a.message||`HTTP ${b.status}: ${b.statusText}`)}let c=await b.json();return c.data||c}catch(a){throw console.error("Failed to get video info:",a),a instanceof Error?a:Error("Failed to get video information")}}async getConversionStatus(a){try{let b=await fetch(`${this.baseUrl}/convert/status?id=${encodeURIComponent(a)}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!b.ok){let a=await b.json().catch(()=>({}));throw Error(a.message||`HTTP ${b.status}: ${b.statusText}`)}let c=await b.json();return c.data||c}catch(a){throw console.error("Failed to get conversion status:",a),a instanceof Error?a:Error("Failed to get conversion status")}}async getSupportedOptions(){try{let a=await fetch(`${this.baseUrl}/convert/options`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok){let b=await a.json().catch(()=>({}));throw Error(b.message||`HTTP ${a.status}: ${a.statusText}`)}let b=await a.json();return b.data||b}catch(a){return console.error("Failed to get supported options:",a),{bitrates:[128,192,320],durationFactors:[1,1.5,2],formats:["mp3"]}}}async healthCheck(){try{let a=await fetch(`${this.baseUrl}/health`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error(`HTTP ${a.status}: ${a.statusText}`);let b=await a.json();return b.data||b}catch(a){return console.error("Health check failed:",a),{status:"error",services:{api:"error"}}}}async cancelConversion(a){try{let b=await fetch(`${this.baseUrl}/convert/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!b.ok){let a=await b.json().catch(()=>({}));throw Error(a.message||`HTTP ${b.status}: ${b.statusText}`)}return!0}catch(a){return console.error("Failed to cancel conversion:",a),!1}}async getPopularDownloads(a=10){try{let b=await fetch(`${this.baseUrl}/popular?limit=${a}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!b.ok){let a=await b.json().catch(()=>({}));throw Error(a.message||`HTTP ${b.status}: ${b.statusText}`)}return(await b.json()).data||[]}catch(a){throw console.error("Failed to get popular downloads:",a),a instanceof Error?a:Error("Failed to get popular downloads")}}async retryRequest(a,b=3,c=1e3){let d;for(let e=0;e<=b;e++)try{return await a()}catch(f){if(d=f instanceof Error?f:Error("Unknown error"),e===b)break;let a=c*Math.pow(2,e)+1e3*Math.random();await new Promise(b=>setTimeout(b,a))}throw d}async isApiAvailable(){try{let a=await this.healthCheck();return"healthy"===a.status||"degraded"===a.status}catch{return!1}}getBaseUrl(){return this.baseUrl}setBaseUrl(a){this.baseUrl=a}}let e=new d},51472:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(75338),e=c(7840),f=c.n(e),g=c(33550),h=c.n(g);c(61135);var i=c(24169),j=c(11172);let k={title:"YouTube to MP3 Converter - Free Online Video to Audio Converter",description:"Convert YouTube videos to MP3 audio files for free. High quality audio conversion with multiple bitrate options. Fast, secure, and easy to use."};function l({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsxs)(j.AuthProvider,{children:[(0,d.jsx)(i.default,{}),a]})})})}},61135:()=>{},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(97523);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},70798:(a,b,c)=>{"use strict";c.d(b,{N:()=>g});var d=c(28913);let e=process.env.NEXT_PUBLIC_SUPABASE_URL,f=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!e||!f)throw Error("Missing Supabase environment variables");let g=(0,d.UU)(e,f,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}})},75650:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,54160,23)),Promise.resolve().then(c.t.bind(c,31603,23)),Promise.resolve().then(c.t.bind(c,68495,23)),Promise.resolve().then(c.t.bind(c,75170,23)),Promise.resolve().then(c.t.bind(c,77526,23)),Promise.resolve().then(c.t.bind(c,78922,23)),Promise.resolve().then(c.t.bind(c,29234,23)),Promise.resolve().then(c.t.bind(c,12263,23)),Promise.resolve().then(c.bind(c,82146))},82274:(a,b,c)=>{Promise.resolve().then(c.bind(c,24169)),Promise.resolve().then(c.bind(c,11172))},93855:(a,b,c)=>{"use strict";c.d(b,{default:()=>w});var d=c(21124),e=c(38301),f=c(3991),g=c.n(f),h=c(42378),i=c(78733),j=c(15303),k=c(67748),l=c(12454),m=c(22815),n=c(5227),o=c(78711),p=c(75219),q=c(18310),r=c(91292),s=c(58620),t=c(81296),u=c(45478);function v({showDetails:a=!1,className:b=""}){let[c,f]=(0,e.useState)(null),[g,h]=(0,e.useState)(!0),[i,j]=(0,e.useState)(null),k=async()=>{try{h(!0);let a=await u.K.healthCheck();f(a),j(new Date)}catch(a){console.error("Health check failed:",a),f({status:"error",services:{web:"healthy",lambda:"error",database:"unknown"},timestamp:new Date().toISOString()}),j(new Date)}finally{h(!1)}},l=a=>{switch(a){case"healthy":return(0,d.jsx)(p.A,{className:"w-4 h-4 text-green-500"});case"degraded":return(0,d.jsx)(q.A,{className:"w-4 h-4 text-yellow-500"});case"error":return(0,d.jsx)(r.A,{className:"w-4 h-4 text-red-500"});default:return(0,d.jsx)(s.A,{className:"w-4 h-4 text-gray-400"})}};return a||c?.status!=="healthy"?(0,d.jsx)(n.N,{children:(0,d.jsxs)(o.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:`${b}`,children:[(0,d.jsxs)("div",{className:`inline-flex items-center px-3 py-2 rounded-lg border ${c?(a=>{switch(a){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"degraded":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"error":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(c.status):"text-gray-600 bg-gray-50 border-gray-200"}`,children:[g?(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}):(0,d.jsx)("div",{className:"mr-2",children:c?l(c.status):(0,d.jsx)(t.A,{className:"w-4 h-4"})}),(0,d.jsx)("span",{className:"text-sm font-medium",children:g?"Checking...":c?`API ${c.status}`:"API Offline"}),a&&(0,d.jsxs)("span",{className:"text-xs ml-2 opacity-75",children:["(",(()=>{if(!i)return"Never";let a=Math.floor((new Date().getTime()-i.getTime())/1e3);return a<60?`${a}s ago`:a<3600?`${Math.floor(a/60)}m ago`:i.toLocaleTimeString()})(),")"]})]}),a&&c&&(0,d.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-2 p-3 bg-white rounded-lg border border-gray-200 shadow-sm",children:(0,d.jsxs)("div",{className:"text-sm space-y-2",children:[(0,d.jsx)("div",{className:"font-medium text-gray-900 mb-2",children:"Service Status:"}),Object.entries(c.services).map(([a,b])=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"capitalize text-gray-600",children:[a,":"]}),(0,d.jsxs)("div",{className:"flex items-center",children:[l(b),(0,d.jsx)("span",{className:`ml-1 text-xs font-medium ${"healthy"===b?"text-green-600":"degraded"===b?"text-yellow-600":"error"===b?"text-red-600":"text-gray-600"}`,children:b})]})]},a)),(0,d.jsx)("div",{className:"pt-2 mt-2 border-t border-gray-100",children:(0,d.jsx)("button",{onClick:k,disabled:g,className:"text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50",children:g?"Checking...":"Refresh Status"})})]})})]})}):null}function w(){let a=(0,h.usePathname)(),[b,c]=(0,e.useState)(!1),[f,n]=(0,e.useState)(!1),o=[{href:"/",label:"Converter",icon:i.A,active:"/"===a},{href:"/history",label:"History",icon:j.A,active:"/history"===a},{href:"/popular",label:"Popular",icon:k.A,active:"/popular"===a}];return(0,d.jsxs)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:[(0,d.jsx)("div",{className:"max-w-6xl mx-auto px-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(l.A,{className:"w-8 h-8 text-red-600"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"YT2MP3"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,d.jsx)("div",{className:"flex space-x-8",children:o.map(a=>{let b=a.icon;return(0,d.jsxs)(g(),{href:a.href,className:`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${a.active?"bg-red-100 text-red-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[(0,d.jsx)(b,{className:"w-5 h-5"}),(0,d.jsx)("span",{className:"font-medium",children:a.label})]},a.href)})}),(0,d.jsx)(v,{className:"hidden md:block"}),(0,d.jsx)("div",{className:"relative",children:(0,d.jsxs)("button",{onClick:()=>c(!0),className:"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors opacity-50 cursor-not-allowed",disabled:!0,children:[(0,d.jsx)(m.A,{className:"w-5 h-5"}),(0,d.jsx)("span",{className:"font-medium",children:"Sign In (Coming Soon)"})]})})]})]})}),f&&(0,d.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>n(!1)})]})}}};