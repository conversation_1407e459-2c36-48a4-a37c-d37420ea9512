(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[429],{1840:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(5155),o=r(2115),a=r(1733),n=r(2529),i=r(8874),l=r(6983),c=r(3586),u=r(9867),d=r(2619),m=r.n(d),h=r(9269),p=r(4597),g=r(2549);function b(){let[e,t]=(0,o.useState)([]),[r,d]=(0,o.useState)(!0),[b,v]=(0,o.useState)(null),{user:y,guestId:w,isAuthenticated:x}=(0,h.A)();return(0,o.useEffect)(()=>((async()=>{try{if(d(!0),v(null),x&&y){let e=await p.v.getUserConversions(y.id);t(e),g.Z.subscribeToUserConversions(y.id,e=>{t(t=>{let r=t.findIndex(t=>t.id===e.id);if(!(r>=0))return[e,...t];{let s=[...t];return s[r]=e,s}})},e=>{console.error("Real-time subscription error:",e)})}else t([])}catch(e){v(e instanceof Error?e.message:"Failed to load history")}finally{d(!1)}})(),()=>{y&&g.Z.unsubscribeFromUserConversions(y.id)}),[y,x,w]),(0,s.jsx)("div",{className:"min-h-screen py-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(l.A,{className:"w-10 h-10 text-blue-600 mr-3"}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900",children:"Conversion History"})]}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:"Track your YouTube to MP3 conversions"})]}),(0,s.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"mb-8",children:(0,s.jsx)(m(),{href:"/",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Converter"})}),(0,s.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white rounded-2xl shadow-lg overflow-hidden",children:r?(0,s.jsxs)("div",{className:"p-12 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Loading history..."}),(0,s.jsx)("p",{className:"text-gray-500",children:"Please wait while we fetch your conversions."})]}):b?(0,s.jsxs)("div",{className:"p-12 text-center",children:[(0,s.jsx)(i.A,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Error loading history"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:b}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]}):x?0===e.length?(0,s.jsxs)("div",{className:"p-12 text-center",children:[(0,s.jsx)(c.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No conversions yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Start converting YouTube videos to see your history here."}),(0,s.jsx)(m(),{href:"/",className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Start Converting"})]}):(0,s.jsx)("div",{className:"divide-y divide-gray-200",children:e.map((e,t)=>(0,s.jsx)(a.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4,delay:.1*t},className:"p-6 hover:bg-gray-50 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(e=>{switch(e){case"completed":return(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500"});case"processing":return(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"});case"failed":return(0,s.jsx)(i.A,{className:"w-5 h-5 text-red-500"});default:return(0,s.jsx)(l.A,{className:"w-5 h-5 text-yellow-500"})}})(e.status),(0,s.jsx)("span",{className:"ml-2 text-sm font-medium text-gray-600",children:(e=>{switch(e){case"completed":return"Completed";case"processing":return"Processing";case"failed":return"Failed";default:return"Pending"}})(e.status)}),(0,s.jsx)("span",{className:"ml-4 text-sm text-gray-500",children:(e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/6e4);return r<60?"".concat(r," minutes ago"):r<1440?"".concat(Math.floor(r/60)," hours ago"):"".concat(Math.floor(r/1440)," days ago")})(e.createdAt)})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1 truncate",children:e.videoTitle}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600 mb-2",children:[(0,s.jsxs)("span",{children:["Quality: ",e.bitrate,"kbps"]}),(0,s.jsxs)("span",{children:["Speed: ",e.durationFactor,"x"]})]}),(0,s.jsx)("a",{href:e.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800 truncate block",children:e.youtubeUrl})]}),(0,s.jsxs)("div",{className:"ml-4 flex-shrink-0",children:["completed"===e.status&&e.mp3Url&&(0,s.jsxs)("a",{href:e.mp3Url,download:!0,className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Download"]}),"failed"===e.status&&(0,s.jsx)("button",{className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Retry"})]})]})},e.id))}):(0,s.jsxs)("div",{className:"p-12 text-center",children:[(0,s.jsx)(c.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Sign in to view history"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Create an account or sign in to track your conversion history."}),(0,s.jsx)(m(),{href:"/",className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Go to Converter"})]})})]})})}},2529:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(1847).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2549:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(2890);class o{subscribeToConversion(e,t,r){this.unsubscribeFromConversion(e);let o=s.N.channel("conversion_progress:".concat(e)).on("postgres_changes",{event:"UPDATE",schema:"public",table:"conversions",filter:"id=eq.".concat(e)},e=>{try{let r=e.new,s={conversionId:r.id,status:r.status,progress:r.progress||0,currentStep:r.current_step||"Starting...",error:r.error_message};t(s)}catch(e){null==r||r(e)}}).on("presence",{event:"sync"},()=>{console.log("Realtime connection synced for conversion:",e)}).on("presence",{event:"join"},e=>{let{key:t,newPresences:r}=e;console.log("Joined realtime channel:",t,r)}).on("presence",{event:"leave"},e=>{let{key:t,leftPresences:r}=e;console.log("Left realtime channel:",t,r)}).subscribe(t=>{"SUBSCRIBED"===t?console.log("Successfully subscribed to conversion updates:",e):"CHANNEL_ERROR"===t&&(console.error("Error subscribing to conversion updates:",e),null==r||r(Error("Failed to subscribe to realtime updates")))});return this.subscriptions.set(e,o),o}unsubscribeFromConversion(e){let t=this.subscriptions.get(e);t&&(s.N.removeChannel(t),this.subscriptions.delete(e),console.log("Unsubscribed from conversion updates:",e))}subscribeToUserConversions(e,t,r){let o="user_conversions:".concat(e);this.unsubscribeFromUserConversions(e);let a=s.N.channel(o).on("postgres_changes",{event:"*",schema:"public",table:"conversions",filter:"user_id=eq.".concat(e)},e=>{try{if(e.new){let r=this.mapConversionFromDb(e.new);t(r)}}catch(e){null==r||r(e)}}).subscribe(t=>{"SUBSCRIBED"===t?console.log("Successfully subscribed to user conversions:",e):"CHANNEL_ERROR"===t&&(console.error("Error subscribing to user conversions:",e),null==r||r(Error("Failed to subscribe to user conversion updates")))});return this.subscriptions.set(o,a),a}unsubscribeFromUserConversions(e){let t="user_conversions:".concat(e),r=this.subscriptions.get(t);r&&(s.N.removeChannel(r),this.subscriptions.delete(t),console.log("Unsubscribed from user conversions:",e))}subscribeToPopularDownloads(e,t){let r="popular_downloads";this.unsubscribeFromPopularDownloads();let o=s.N.channel(r).on("postgres_changes",{event:"*",schema:"public",table:"popular_downloads"},async r=>{try{let{data:r,error:o}=await s.N.from("popular_downloads").select("*").order("daily_count",{ascending:!1}).limit(10);if(o){null==t||t(Error(o.message));return}e(r||[])}catch(e){null==t||t(e)}}).subscribe(e=>{"SUBSCRIBED"===e?console.log("Successfully subscribed to popular downloads"):"CHANNEL_ERROR"===e&&(console.error("Error subscribing to popular downloads"),null==t||t(Error("Failed to subscribe to popular downloads updates")))});return this.subscriptions.set(r,o),o}unsubscribeFromPopularDownloads(){let e="popular_downloads",t=this.subscriptions.get(e);t&&(s.N.removeChannel(t),this.subscriptions.delete(e),console.log("Unsubscribed from popular downloads"))}unsubscribeAll(){this.subscriptions.forEach((e,t)=>{s.N.removeChannel(e),console.log("Unsubscribed from channel:",t)}),this.subscriptions.clear()}getConnectionStatus(){return s.N.realtime.isConnected()}reconnect(){s.N.realtime.disconnect(),s.N.realtime.connect()}mapConversionFromDb(e){return{id:e.id,userId:e.user_id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,mp3Url:e.mp3_url,status:e.status,bitrate:e.bitrate,durationFactor:e.duration_factor,progress:e.progress||0,currentStep:e.current_step,error:e.error_message,createdAt:e.created_at,completedAt:e.completed_at,fileSize:e.file_size,duration:e.duration}}constructor(){this.subscriptions=new Map}}let a=new o},2890:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(5040),o=r(8969);let a=o.env.NEXT_PUBLIC_SUPABASE_URL,n=o.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!a||!n)throw Error("Missing Supabase environment variables");let i=(0,s.UU)(a,n,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}})},4597:(e,t,r)=>{"use strict";r.d(t,{v:()=>a});var s=r(2890);class o{async createConversion(e){let{data:t,error:r}=await s.N.from("conversions").insert({user_id:e.userId,youtube_url:e.youtubeUrl,video_title:e.videoTitle,bitrate:e.bitrate,duration_factor:e.durationFactor,status:e.status||"pending",progress:e.progress||0,current_step:e.currentStep}).select().single();if(r)throw Error("Failed to create conversion: ".concat(r.message));return this.mapConversionFromDb(t)}async updateConversion(e,t){let r={};t.status&&(r.status=t.status),void 0!==t.progress&&(r.progress=t.progress),t.currentStep&&(r.current_step=t.currentStep),t.mp3Url&&(r.mp3_url=t.mp3Url),t.error&&(r.error_message=t.error),t.completedAt&&(r.completed_at=t.completedAt);let{data:o,error:a}=await s.N.from("conversions").update(r).eq("id",e).select().single();if(a)throw Error("Failed to update conversion: ".concat(a.message));return this.mapConversionFromDb(o)}async getConversion(e){let{data:t,error:r}=await s.N.from("conversions").select("*").eq("id",e).single();if(r){if("PGRST116"===r.code)return null;throw Error("Failed to get conversion: ".concat(r.message))}return this.mapConversionFromDb(t)}async getUserConversions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,{data:r,error:o}=await s.N.from("conversions").select("*").eq("user_id",e).order("created_at",{ascending:!1}).limit(t);if(o)throw Error("Failed to get user conversions: ".concat(o.message));return r.map(this.mapConversionFromDb)}async getRecentConversions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,{data:t,error:r}=await s.N.from("conversions").select("*").order("created_at",{ascending:!1}).limit(e);if(r)throw Error("Failed to get recent conversions: ".concat(r.message));return t.map(this.mapConversionFromDb)}async updatePopularDownload(e,t,r){let{data:o}=await s.N.from("popular_downloads").select("*").eq("youtube_url",e).single();if(o){let{error:e}=await s.N.from("popular_downloads").update({download_count:o.download_count+1,daily_count:o.daily_count+1,last_updated:new Date().toISOString(),video_title:t,thumbnail_url:r||o.thumbnail_url}).eq("id",o.id);if(e)throw Error("Failed to update popular download: ".concat(e.message))}else{let{error:o}=await s.N.from("popular_downloads").insert({youtube_url:e,video_title:t,download_count:1,daily_count:1,thumbnail_url:r,last_updated:new Date().toISOString()});if(o)throw Error("Failed to create popular download: ".concat(o.message))}}async getPopularDownloads(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,{data:t,error:r}=await s.N.from("popular_downloads").select("*").order("daily_count",{ascending:!1}).limit(e);if(r)throw Error("Failed to get popular downloads: ".concat(r.message));return t.map(e=>({id:e.id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,thumbnailUrl:e.thumbnail_url||"",downloadCount:e.download_count,dailyCount:e.daily_count,lastUpdated:e.last_updated}))}async resetDailyCounts(){let{error:e}=await s.N.from("popular_downloads").update({daily_count:0});if(e)throw Error("Failed to reset daily counts: ".concat(e.message))}subscribeToConversion(e,t){return s.N.channel("conversion:".concat(e)).on("postgres_changes",{event:"UPDATE",schema:"public",table:"conversions",filter:"id=eq.".concat(e)},e=>{t(this.mapConversionFromDb(e.new))}).subscribe()}subscribeToUserConversions(e,t){return s.N.channel("user_conversions:".concat(e)).on("postgres_changes",{event:"*",schema:"public",table:"conversions",filter:"user_id=eq.".concat(e)},e=>{e.new&&t(this.mapConversionFromDb(e.new))}).subscribe()}mapConversionFromDb(e){return{id:e.id,userId:e.user_id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,mp3Url:e.mp3_url,status:e.status,bitrate:e.bitrate,durationFactor:e.duration_factor,progress:e.progress||0,currentStep:e.current_step,error:e.error_message,createdAt:e.created_at,completedAt:e.completed_at,fileSize:e.file_size,duration:e.duration}}async cleanupOldConversions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30,t=new Date;t.setDate(t.getDate()-e);let{data:r,error:o}=await s.N.from("conversions").delete().lt("created_at",t.toISOString()).select("id");if(o)throw Error("Failed to cleanup old conversions: ".concat(o.message));return(null==r?void 0:r.length)||0}}let a=new o},6983:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(1847).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},8874:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(1847).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},9269:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>u,A:()=>c});var s=r(5155),o=r(2115),a=r(2890);class n{async signUp(e,t,r){let{data:s,error:o}=await a.N.auth.signUp({email:e,password:t,options:{data:{display_name:r}}});if(o)throw Error(o.message);return s}async signIn(e,t){let{data:r,error:s}=await a.N.auth.signInWithPassword({email:e,password:t});if(s)throw Error(s.message);return r}async signInWithGoogle(){let{data:e,error:t}=await a.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback")}});if(t)throw Error(t.message);return e}async signOut(){let{error:e}=await a.N.auth.signOut();if(e)throw Error(e.message)}async getCurrentUser(){let{data:{user:e}}=await a.N.auth.getUser();return e?this.mapUser(e):null}async getCurrentSession(){let{data:{session:e}}=await a.N.auth.getSession();return e}async updateProfile(e){let{data:t,error:r}=await a.N.auth.updateUser({data:{display_name:e.displayName,avatar_url:e.avatarUrl}});if(r)throw Error(r.message);return t}async resetPassword(e){let{error:t}=await a.N.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(t)throw Error(t.message)}async updatePassword(e){let{data:t,error:r}=await a.N.auth.updateUser({password:e});if(r)throw Error(r.message);return t}onAuthStateChange(e){return a.N.auth.onAuthStateChange((t,r)=>{e((null==r?void 0:r.user)?this.mapUser(r.user):null)})}async isAuthenticated(){return!!await this.getCurrentSession()}async getAccessToken(){let e=await this.getCurrentSession();return(null==e?void 0:e.access_token)||null}async refreshSession(){let{data:e,error:t}=await a.N.auth.refreshSession();if(t)throw Error(t.message);return e}mapUser(e){var t,r,s;return{id:e.id,email:e.email||"",displayName:(null==(t=e.user_metadata)?void 0:t.display_name)||(null==(r=e.user_metadata)?void 0:r.full_name),avatarUrl:null==(s=e.user_metadata)?void 0:s.avatar_url,createdAt:e.created_at}}generateGuestId(){let e=localStorage.getItem("guest_id");if(e)return e;let t="guest_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9));return localStorage.setItem("guest_id",t),t}clearGuestSession(){localStorage.removeItem("guest_id")}async isGuestSession(){return!await this.isAuthenticated()&&!!localStorage.getItem("guest_id")}}let i=new n,l=(0,o.createContext)(void 0);function c(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(e){let{children:t}=e,[r,a]=(0,o.useState)(null),[n,c]=(0,o.useState)(!0),[u,d]=(0,o.useState)(null);(0,o.useEffect)(()=>{(async()=>{try{let e=await i.getCurrentUser();if(a(e),!e){let e=i.generateGuestId();d(e)}}catch(e){console.error("Error initializing auth:",e)}finally{c(!1)}})();let{data:{subscription:e}}=i.onAuthStateChange(e=>{a(e),e?(i.clearGuestSession(),d(null)):d(i.generateGuestId()),c(!1)});return()=>{null==e||e.unsubscribe()}},[]);let m=async(e,t)=>{c(!0);try{await i.signIn(e,t)}catch(e){throw c(!1),e}},h=async(e,t,r)=>{c(!0);try{await i.signUp(e,t,r)}catch(e){throw c(!1),e}},p=async()=>{c(!0);try{await i.signInWithGoogle()}catch(e){throw c(!1),e}},g=async()=>{c(!0);try{await i.signOut()}catch(e){throw c(!1),e}},b=async e=>{try{await i.updateProfile(e);let t=await i.getCurrentUser();a(t)}catch(e){throw e}},v=async e=>{try{await i.resetPassword(e)}catch(e){throw e}};return(0,s.jsx)(l.Provider,{value:{user:r,loading:n,signIn:m,signUp:h,signInWithGoogle:p,signOut:g,updateProfile:b,resetPassword:v,isAuthenticated:!!r,guestId:u},children:t})}},9867:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(1847).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},9985:(e,t,r)=>{Promise.resolve().then(r.bind(r,1840))}},e=>{e.O(0,[180,40,441,255,358],()=>e(e.s=9985)),_N_E=e.O()}]);