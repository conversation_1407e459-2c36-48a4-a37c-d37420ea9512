(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{960:(e,t,a)=>{"use strict";a.d(t,{K:()=>n});var r=a(8969);class s{async startConversion(e){try{let t=await fetch("".concat(this.baseUrl,"/convert"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}let a=await t.json();return a.data||a}catch(e){throw console.error("Failed to start conversion:",e),e instanceof Error?e:Error("Failed to start conversion")}}async getVideoInfo(e){try{let t=await fetch("".concat(this.baseUrl,"/convert/info"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeUrl:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}let a=await t.json();return a.data||a}catch(e){throw console.error("Failed to get video info:",e),e instanceof Error?e:Error("Failed to get video information")}}async getConversionStatus(e){try{let t=await fetch("".concat(this.baseUrl,"/convert/status?id=").concat(encodeURIComponent(e)),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}let a=await t.json();return a.data||a}catch(e){throw console.error("Failed to get conversion status:",e),e instanceof Error?e:Error("Failed to get conversion status")}}async getSupportedOptions(){try{let e=await fetch("".concat(this.baseUrl,"/convert/options"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"HTTP ".concat(e.status,": ").concat(e.statusText))}let t=await e.json();return t.data||t}catch(e){return console.error("Failed to get supported options:",e),{bitrates:[128,192,320],durationFactors:[1,1.5,2],formats:["mp3"]}}}async healthCheck(){try{let e=await fetch("".concat(this.baseUrl,"/health"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP ".concat(e.status,": ").concat(e.statusText));let t=await e.json();return t.data||t}catch(e){return console.error("Health check failed:",e),{status:"error",services:{api:"error"}}}}async cancelConversion(e){try{let t=await fetch("".concat(this.baseUrl,"/convert/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}return!0}catch(e){return console.error("Failed to cancel conversion:",e),!1}}async getPopularDownloads(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let t=await fetch("".concat(this.baseUrl,"/popular?limit=").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}return(await t.json()).data||[]}catch(e){throw console.error("Failed to get popular downloads:",e),e instanceof Error?e:Error("Failed to get popular downloads")}}async retryRequest(e){let t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let s=0;s<=a;s++)try{return await e()}catch(n){if(t=n instanceof Error?n:Error("Unknown error"),s===a)break;let e=r*Math.pow(2,s)+1e3*Math.random();await new Promise(t=>setTimeout(t,e))}throw t}async isApiAvailable(){try{let e=await this.healthCheck();return"healthy"===e.status||"degraded"===e.status}catch(e){return!1}}getBaseUrl(){return this.baseUrl}setBaseUrl(e){this.baseUrl=e}constructor(){this.baseUrl=r.env.NEXT_PUBLIC_API_URL||"http://localhost:3000/api"}}let n=new s},1894:(e,t,a)=>{"use strict";a.d(t,{default:()=>N});var r=a(5155),s=a(2115),n=a(2619),o=a.n(n),i=a(63),c=a(5993),l=a(6983),h=a(1524),d=a(3586),u=a(9865),m=a(4342),g=a(1733),w=a(2529),y=a(6132),p=a(8874),x=a(3166),f=a(3738),v=a(960);function b(e){let{showDetails:t=!1,className:a=""}=e,[n,o]=(0,s.useState)(null),[i,c]=(0,s.useState)(!0),[l,h]=(0,s.useState)(null),d=async()=>{try{c(!0);let e=await v.K.healthCheck();o(e),h(new Date)}catch(e){console.error("Health check failed:",e),o({status:"error",services:{web:"healthy",lambda:"error",database:"unknown"},timestamp:new Date().toISOString()}),h(new Date)}finally{c(!1)}};(0,s.useEffect)(()=>{d();let e=setInterval(d,3e4);return()=>clearInterval(e)},[]);let u=e=>{switch(e){case"healthy":return(0,r.jsx)(w.A,{className:"w-4 h-4 text-green-500"});case"degraded":return(0,r.jsx)(y.A,{className:"w-4 h-4 text-yellow-500"});case"error":return(0,r.jsx)(p.A,{className:"w-4 h-4 text-red-500"});default:return(0,r.jsx)(x.A,{className:"w-4 h-4 text-gray-400"})}};return t||(null==n?void 0:n.status)!=="healthy"?(0,r.jsx)(m.N,{children:(0,r.jsxs)(g.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"".concat(a),children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-3 py-2 rounded-lg border ".concat(n?(e=>{switch(e){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"degraded":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"error":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(n.status):"text-gray-600 bg-gray-50 border-gray-200"),children:[i?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}):(0,r.jsx)("div",{className:"mr-2",children:n?u(n.status):(0,r.jsx)(f.A,{className:"w-4 h-4"})}),(0,r.jsx)("span",{className:"text-sm font-medium",children:i?"Checking...":n?"API ".concat(n.status):"API Offline"}),t&&(0,r.jsxs)("span",{className:"text-xs ml-2 opacity-75",children:["(",(()=>{if(!l)return"Never";let e=Math.floor((new Date().getTime()-l.getTime())/1e3);return e<60?"".concat(e,"s ago"):e<3600?"".concat(Math.floor(e/60),"m ago"):l.toLocaleTimeString()})(),")"]})]}),t&&n&&(0,r.jsx)(g.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-2 p-3 bg-white rounded-lg border border-gray-200 shadow-sm",children:(0,r.jsxs)("div",{className:"text-sm space-y-2",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 mb-2",children:"Service Status:"}),Object.entries(n.services).map(e=>{let[t,a]=e;return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"capitalize text-gray-600",children:[t,":"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[u(a),(0,r.jsx)("span",{className:"ml-1 text-xs font-medium ".concat("healthy"===a?"text-green-600":"degraded"===a?"text-yellow-600":"error"===a?"text-red-600":"text-gray-600"),children:a})]})]},t)}),(0,r.jsx)("div",{className:"pt-2 mt-2 border-t border-gray-100",children:(0,r.jsx)("button",{onClick:d,disabled:i,className:"text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50",children:i?"Checking...":"Refresh Status"})})]})})]})}):null}function N(){let e=(0,i.usePathname)(),[t,a]=(0,s.useState)(!1),[n,m]=(0,s.useState)(!1),g=[{href:"/",label:"Converter",icon:c.A,active:"/"===e},{href:"/history",label:"History",icon:l.A,active:"/history"===e},{href:"/popular",label:"Popular",icon:h.A,active:"/popular"===e}];return(0,r.jsxs)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:[(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{className:"w-8 h-8 text-red-600"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"YT2MP3"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsx)("div",{className:"flex space-x-8",children:g.map(e=>{let t=e.icon;return(0,r.jsxs)(o(),{href:e.href,className:"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ".concat(e.active?"bg-red-100 text-red-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,r.jsx)(t,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"font-medium",children:e.label})]},e.href)})}),(0,r.jsx)(b,{className:"hidden md:block"}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{onClick:()=>a(!0),className:"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors opacity-50 cursor-not-allowed",disabled:!0,children:[(0,r.jsx)(u.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"font-medium",children:"Sign In (Coming Soon)"})]})})]})]})}),n&&(0,r.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>m(!1)})]})}},2890:(e,t,a)=>{"use strict";a.d(t,{N:()=>i});var r=a(5040),s=a(8969);let n=s.env.NEXT_PUBLIC_SUPABASE_URL,o=s.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!n||!o)throw Error("Missing Supabase environment variables");let i=(0,r.UU)(n,o,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}})},3673:()=>{},5478:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,6168,23)),Promise.resolve().then(a.t.bind(a,3944,23)),Promise.resolve().then(a.t.bind(a,3673,23)),Promise.resolve().then(a.bind(a,1894)),Promise.resolve().then(a.bind(a,9269))},9269:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>h,A:()=>l});var r=a(5155),s=a(2115),n=a(2890);class o{async signUp(e,t,a){let{data:r,error:s}=await n.N.auth.signUp({email:e,password:t,options:{data:{display_name:a}}});if(s)throw Error(s.message);return r}async signIn(e,t){let{data:a,error:r}=await n.N.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message);return a}async signInWithGoogle(){let{data:e,error:t}=await n.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback")}});if(t)throw Error(t.message);return e}async signOut(){let{error:e}=await n.N.auth.signOut();if(e)throw Error(e.message)}async getCurrentUser(){let{data:{user:e}}=await n.N.auth.getUser();return e?this.mapUser(e):null}async getCurrentSession(){let{data:{session:e}}=await n.N.auth.getSession();return e}async updateProfile(e){let{data:t,error:a}=await n.N.auth.updateUser({data:{display_name:e.displayName,avatar_url:e.avatarUrl}});if(a)throw Error(a.message);return t}async resetPassword(e){let{error:t}=await n.N.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(t)throw Error(t.message)}async updatePassword(e){let{data:t,error:a}=await n.N.auth.updateUser({password:e});if(a)throw Error(a.message);return t}onAuthStateChange(e){return n.N.auth.onAuthStateChange((t,a)=>{e((null==a?void 0:a.user)?this.mapUser(a.user):null)})}async isAuthenticated(){return!!await this.getCurrentSession()}async getAccessToken(){let e=await this.getCurrentSession();return(null==e?void 0:e.access_token)||null}async refreshSession(){let{data:e,error:t}=await n.N.auth.refreshSession();if(t)throw Error(t.message);return e}mapUser(e){var t,a,r;return{id:e.id,email:e.email||"",displayName:(null==(t=e.user_metadata)?void 0:t.display_name)||(null==(a=e.user_metadata)?void 0:a.full_name),avatarUrl:null==(r=e.user_metadata)?void 0:r.avatar_url,createdAt:e.created_at}}generateGuestId(){let e=localStorage.getItem("guest_id");if(e)return e;let t="guest_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9));return localStorage.setItem("guest_id",t),t}clearGuestSession(){localStorage.removeItem("guest_id")}async isGuestSession(){return!await this.isAuthenticated()&&!!localStorage.getItem("guest_id")}}let i=new o,c=(0,s.createContext)(void 0);function l(){let e=(0,s.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h(e){let{children:t}=e,[a,n]=(0,s.useState)(null),[o,l]=(0,s.useState)(!0),[h,d]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{let e=await i.getCurrentUser();if(n(e),!e){let e=i.generateGuestId();d(e)}}catch(e){console.error("Error initializing auth:",e)}finally{l(!1)}})();let{data:{subscription:e}}=i.onAuthStateChange(e=>{n(e),e?(i.clearGuestSession(),d(null)):d(i.generateGuestId()),l(!1)});return()=>{null==e||e.unsubscribe()}},[]);let u=async(e,t)=>{l(!0);try{await i.signIn(e,t)}catch(e){throw l(!1),e}},m=async(e,t,a)=>{l(!0);try{await i.signUp(e,t,a)}catch(e){throw l(!1),e}},g=async()=>{l(!0);try{await i.signInWithGoogle()}catch(e){throw l(!1),e}},w=async()=>{l(!0);try{await i.signOut()}catch(e){throw l(!1),e}},y=async e=>{try{await i.updateProfile(e);let t=await i.getCurrentUser();n(t)}catch(e){throw e}},p=async e=>{try{await i.resetPassword(e)}catch(e){throw e}};return(0,r.jsx)(c.Provider,{value:{user:a,loading:o,signIn:u,signUp:m,signInWithGoogle:g,signOut:w,updateProfile:y,resetPassword:p,isAuthenticated:!!a,guestId:h},children:t})}}},e=>{e.O(0,[652,180,40,44,441,255,358],()=>e(e.s=5478)),_N_E=e.O()}]);