(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{948:(e,t,s)=>{Promise.resolve().then(s.bind(s,8169))},960:(e,t,s)=>{"use strict";s.d(t,{K:()=>o});var r=s(8969);class a{async startConversion(e){try{let t=await fetch("".concat(this.baseUrl,"/convert"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}let s=await t.json();return s.data||s}catch(e){throw console.error("Failed to start conversion:",e),e instanceof Error?e:<PERSON>rror("Failed to start conversion")}}async getVideoInfo(e){try{let t=await fetch("".concat(this.baseUrl,"/convert/info"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeUrl:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}let s=await t.json();return s.data||s}catch(e){throw console.error("Failed to get video info:",e),e instanceof Error?e:Error("Failed to get video information")}}async getConversionStatus(e){try{let t=await fetch("".concat(this.baseUrl,"/convert/status?id=").concat(encodeURIComponent(e)),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}let s=await t.json();return s.data||s}catch(e){throw console.error("Failed to get conversion status:",e),e instanceof Error?e:Error("Failed to get conversion status")}}async getSupportedOptions(){try{let e=await fetch("".concat(this.baseUrl,"/convert/options"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"HTTP ".concat(e.status,": ").concat(e.statusText))}let t=await e.json();return t.data||t}catch(e){return console.error("Failed to get supported options:",e),{bitrates:[128,192,320],durationFactors:[1,1.5,2],formats:["mp3"]}}}async healthCheck(){try{let e=await fetch("".concat(this.baseUrl,"/health"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP ".concat(e.status,": ").concat(e.statusText));let t=await e.json();return t.data||t}catch(e){return console.error("Health check failed:",e),{status:"error",services:{api:"error"}}}}async cancelConversion(e){try{let t=await fetch("".concat(this.baseUrl,"/convert/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}return!0}catch(e){return console.error("Failed to cancel conversion:",e),!1}}async getPopularDownloads(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let t=await fetch("".concat(this.baseUrl,"/popular?limit=").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText))}return(await t.json()).data||[]}catch(e){throw console.error("Failed to get popular downloads:",e),e instanceof Error?e:Error("Failed to get popular downloads")}}async retryRequest(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let a=0;a<=s;a++)try{return await e()}catch(o){if(t=o instanceof Error?o:Error("Unknown error"),a===s)break;let e=r*Math.pow(2,a)+1e3*Math.random();await new Promise(t=>setTimeout(t,e))}throw t}async isApiAvailable(){try{let e=await this.healthCheck();return"healthy"===e.status||"degraded"===e.status}catch(e){return!1}}getBaseUrl(){return this.baseUrl}setBaseUrl(e){this.baseUrl=e}constructor(){this.baseUrl=r.env.NEXT_PUBLIC_API_URL||"http://localhost:3000/api"}}let o=new a},8169:(e,t,s)=>{"use strict";s.d(t,{default:()=>E});var r=s(5155),a=s(2115),o=s(1733),n=s(3586),i=s(3327),l=s(5870),c=s(6154),d=s(9867),u=s(6983),h=s(1524),m=s(2619),p=s.n(m),x=s(2821),g=s(5889);function b(e){return[/^(https?:\/\/)?(www\.)?youtube\.com\/watch\?v=[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/shorts\/[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/embed\/[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/v\/[\w-]+/,/^(https?:\/\/)?youtu\.be\/[\w-]+/,/^(https?:\/\/)?(www\.)?youtube\.com\/watch\?.*v=[\w-]+/].some(t=>t.test(e))}let v=(0,a.forwardRef)((e,t)=>{let{className:s,variant:a="primary",size:o="md",isLoading:n,children:i,disabled:l,...c}=e;return(0,r.jsx)("button",{ref:t,className:function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,g.QP)((0,x.$)(t))}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border-2 border-red-600 text-red-600 hover:bg-red-600 hover:text-white focus:ring-red-500"}[a],{sm:"px-3 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[o],s),disabled:l||n,...c,children:n?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Converting..."]}):i})});v.displayName="Button";var f=s(4342),y=s(2529),w=s(6132),j=s(5229);function N(e){let{message:t,type:s="error",isVisible:n,onClose:l,duration:c=5e3}=e;return(0,a.useEffect)(()=>{if(n&&c>0){let e=setTimeout(()=>{l()},c);return()=>clearTimeout(e)}},[n,c,l]),(0,r.jsx)(f.N,{children:n&&(0,r.jsx)(o.P.div,{initial:{opacity:0,y:-50,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.9},transition:{duration:.3},className:"fixed top-4 right-4 z-50 max-w-md",children:(0,r.jsx)("div",{className:"rounded-lg border p-4 shadow-lg ".concat((()=>{switch(s){case"success":return"bg-green-50 border-green-200 text-green-800";case"info":return"bg-blue-50 border-blue-200 text-blue-800";default:return"bg-red-50 border-red-200 text-red-800"}})()),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(s){case"success":return(0,r.jsx)(y.A,{className:"w-5 h-5 text-green-500"});case"info":return(0,r.jsx)(i.A,{className:"w-5 h-5 text-blue-500"});default:return(0,r.jsx)(w.A,{className:"w-5 h-5 text-red-500"})}})()}),(0,r.jsx)("div",{className:"ml-3 flex-1",children:(0,r.jsx)("p",{className:"text-sm font-medium whitespace-pre-line",children:t})}),(0,r.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,r.jsx)("button",{onClick:l,className:"inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 transition-colors",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})})})]})})})})}var C=s(2348),S=s(960);class P{async startConversion(e,t,s,r){try{let a=await S.K.startConversion(e),o=a.conversionId,n={id:o,userId:e.userId,videoTitle:"Loading...",youtubeUrl:e.youtubeUrl,status:a.status,bitrate:e.bitrate,durationFactor:e.durationFactor,progress:0,currentStep:"Starting conversion...",createdAt:new Date().toISOString()};return this.activeConversions.set(o,n),this.options.enableDatabase,this.options.enableRealtime?this.setupRealtimeUpdates(o,t,s,r):this.setupPolling(o,t,s,r),o}catch(t){let e=t instanceof Error?t.message:"Failed to start conversion";throw null==r||r(Error(e)),t}}async getConversionStatus(e){let t=this.activeConversions.get(e);if(t)return t;this.options.enableDatabase;try{let t=await S.K.getConversionStatus(e),s={id:e,videoTitle:"Unknown",youtubeUrl:"",status:t.status,bitrate:192,durationFactor:1,progress:t.progress,currentStep:t.currentStep,error:t.error,createdAt:new Date().toISOString()};return this.activeConversions.set(e,s),s}catch(e){return console.error("Failed to get conversion status:",e),null}}async cancelConversion(e){try{let t=await S.K.cancelConversion(e);if(t){let t=this.activeConversions.get(e);t&&(t.status="failed",t.error="Cancelled by user",this.activeConversions.set(e,t)),this.options.enableDatabase,this.stopPolling(e)}return t}catch(e){return console.error("Failed to cancel conversion:",e),!1}}getActiveConversions(){return Array.from(this.activeConversions.values())}cleanupCompletedConversions(){for(let[e,t]of this.activeConversions.entries())("completed"===t.status||"failed"===t.status)&&(this.activeConversions.delete(e),this.stopPolling(e))}setupRealtimeUpdates(e,t,s,r){}setupPolling(e,t,s,r){let a=setInterval(async()=>{try{let a=await S.K.getConversionStatus(e),o=this.activeConversions.get(e);o&&(o.status=a.status,o.progress=a.progress,o.currentStep=a.currentStep,o.error=a.error,"completed"===a.status&&(o.completedAt=new Date().toISOString()),this.activeConversions.set(e,o)),null==t||t(a),("completed"===a.status||"failed"===a.status)&&(this.stopPolling(e),o&&("completed"===a.status?null==s||s(o):null==r||r(Error(a.error||"Conversion failed"))))}catch(t){console.error("Polling error for conversion:",e,t),null==r||r(t instanceof Error?t:Error("Polling failed")),this.stopPolling(e)}},this.options.pollInterval);this.pollIntervals.set(e,a)}stopPolling(e){let t=this.pollIntervals.get(e);t&&(clearInterval(t),this.pollIntervals.delete(e))}updateConversionProgress(e,t){let s=this.activeConversions.get(e);s&&(s.status=t.status,s.progress=t.progress,s.currentStep=t.currentStep,s.error=t.error,"completed"===t.status&&(s.completedAt=new Date().toISOString()),this.activeConversions.set(e,s))}cleanup(){for(let e of this.pollIntervals.values())clearInterval(e);this.pollIntervals.clear(),this.activeConversions.clear(),this.options.enableRealtime}constructor(e={}){this.activeConversions=new Map,this.pollIntervals=new Map,this.options={enableDatabase:!1,enableRealtime:!1,pollInterval:2e3,...e}}}let T=new P;function F(e){let{isVisible:t,onClose:s,videoUrl:n,conversionId:i,options:l}=e,[c,u]=(0,a.useState)([{id:"fetch",label:"Fetching video information",status:"pending"},{id:"download",label:"Downloading video",status:"pending",progress:0},{id:"convert",label:"Converting to MP3",status:"pending",progress:0},{id:"upload",label:"Preparing download",status:"pending",progress:0}]),[h,m]=(0,a.useState)(0),[p,x]=(0,a.useState)(0),[g,b]=(0,a.useState)("Calculating..."),[v,f]=(0,a.useState)(null),[j,N]=(0,a.useState)(null);return((0,a.useEffect)(()=>{if(!t||!i)return;let e=setInterval(async()=>{try{let t=await T.getConversionStatus(i);if(!t)return;let s=t.progress||0;x(s),b(s<100?"Processing...":"Completed!"),s<25?(m(0),u(e=>e.map((e,t)=>0===t?{...e,status:"processing"}:e))):s<50?(m(1),u(e=>e.map((e,t)=>0===t?{...e,status:"completed"}:1===t?{...e,status:"processing",progress:(s-25)*4}:e))):s<75?(m(2),u(e=>e.map((e,t)=>t<=1?{...e,status:"completed"}:2===t?{...e,status:"processing",progress:(s-50)*4}:e))):s<100?(m(3),u(e=>e.map((e,t)=>t<=2?{...e,status:"completed"}:3===t?{...e,status:"processing",progress:(s-75)*4}:e))):(u(e=>e.map(e=>({...e,status:"completed"}))),b("Completed!")),"completed"===t.status?(f(t.mp3Url||"https://example.com/download.mp3"),clearInterval(e)):"failed"===t.status&&(N(t.error||"Conversion failed. Please try again."),u(e=>e.map((e,t)=>t===h?{...e,status:"error"}:e)),clearInterval(e))}catch(t){console.error("Failed to poll conversion status:",t),N("Failed to get conversion status"),clearInterval(e)}},2e3);return()=>{clearInterval(e)}},[t,i,h]),t)?(0,r.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)(o.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white rounded-2xl shadow-2xl max-w-md w-full p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Converting Video"}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["Quality: ",l.bitrate,"kbps • Speed: ",l.durationFactor,"x"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[Math.round(p),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)(o.P.div,{className:"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full",initial:{width:0},animate:{width:"".concat(p,"%")},transition:{duration:.5}})}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["Estimated time: ",g]})]}),(0,r.jsx)("div",{className:"space-y-4 mb-6",children:c.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e.status){case"completed":return(0,r.jsx)(y.A,{className:"w-5 h-5 text-green-500"});case"processing":return(0,r.jsx)(C.A,{className:"w-5 h-5 text-blue-500 animate-spin"});case"error":return(0,r.jsx)(w.A,{className:"w-5 h-5 text-red-500"});default:return(0,r.jsx)("div",{className:"w-5 h-5 rounded-full border-2 border-gray-300"})}})(e),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium ".concat("completed"===e.status?"text-green-700":"processing"===e.status?"text-blue-700":"error"===e.status?"text-red-700":"text-gray-500"),children:e.label}),"processing"===e.status&&void 0!==e.progress&&(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1 mt-1",children:(0,r.jsx)(o.P.div,{className:"bg-blue-500 h-1 rounded-full",initial:{width:0},animate:{width:"".concat(e.progress,"%")},transition:{duration:.3}})})]})]},e.id))}),j&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("p",{className:"text-red-700 text-sm",children:j})]})}),(0,r.jsx)("div",{className:"flex space-x-3",children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{href:v,download:!0,className:"flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"Download MP3"]}),(0,r.jsx)("button",{onClick:s,className:"px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Close"})]}):(0,r.jsx)("button",{onClick:s,className:"w-full py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",disabled:!j&&p<100,children:j?"Close":"Cancel"})})]})}):null}function E(){let[e,t]=(0,a.useState)(""),[s,m]=(0,a.useState)({bitrate:192,durationFactor:1}),[x,g]=(0,a.useState)(!1),[f,y]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),[C,P]=(0,a.useState)(!1),[E,k]=(0,a.useState)(null),[A,U]=(0,a.useState)(null),[I,D]=(0,a.useState)(!1),[M,O]=(0,a.useState)(null),[R,H]=(0,a.useState)({message:"",type:"error",isVisible:!1});(0,a.useEffect)(()=>{(async()=>{try{let e=await S.K.getSupportedOptions();O(e)}catch(e){console.warn("Failed to load supported options, using defaults")}})()},[]),(0,a.useEffect)(()=>{let t=setTimeout(async()=>{if(!e||!b(e))return void U(null);D(!0);try{let t=await S.K.getVideoInfo(e);U(t)}catch(e){console.warn("Failed to load video info:",e),U(null)}finally{D(!1)}},1e3);return()=>clearTimeout(t)},[e]);let L=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"error";H({message:e,type:t,isVisible:!0})},V=async()=>{if(!b(e))return void L("Please enter a valid YouTube URL. Supported formats:\n• https://youtube.com/watch?v=...\n• https://youtube.com/shorts/...\n• https://youtu.be/...\n• https://youtube.com/embed/...","error");g(!0),P(!0);try{let t={youtubeUrl:e,bitrate:s.bitrate,durationFactor:s.durationFactor},r=await T.startConversion(t,e=>{console.log("Conversion progress:",e)},e=>{L("Conversion completed successfully!","success"),g(!1)},e=>{L("Conversion failed: ".concat(e.message),"error"),g(!1)});k(r),L("Conversion started successfully!","success")}catch(e){g(!1),P(!1),L(e instanceof Error?e.message:"Failed to start conversion","error")}},_=(null==M?void 0:M.bitrates.map(e=>({value:e,label:"".concat(e," kbps ").concat(128===e?"(Good)":192===e?"(High)":"(Best)")})))||[{value:128,label:"128 kbps (Good)"},{value:192,label:"192 kbps (High)"},{value:320,label:"320 kbps (Best)"}],K=(null==M?void 0:M.durationFactors.map(e=>({value:e,label:"".concat(e,"x ").concat(1===e?"(Normal)":1.5===e?"(Faster)":"(Fastest)")})))||[{value:1,label:"1.0x (Normal)"},{value:1.5,label:"1.5x (Faster)"},{value:2,label:"2.0x (Fastest)"}];return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(n.A,{className:"w-12 h-12 text-red-600 mr-3"}),(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900",children:"YouTube to MP3"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Convert YouTube videos to high-quality MP3 audio files. Fast, free, and easy to use."})]}),(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"bg-white rounded-2xl shadow-2xl p-8 mb-8",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)(o.P.div,{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-0 transition-opacity duration-300 ".concat(w?"opacity-100":"opacity-0"),style:{background:w?"linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6, #ef4444)":"transparent",backgroundSize:"200% 100%",animation:w?"rainbow 2s linear infinite":"none"}}),(0,r.jsx)("div",{className:"relative bg-white rounded-xl p-1",children:(0,r.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Paste YouTube URL here (videos, shorts, etc.)...",className:"w-full px-6 py-4 text-lg border-2 border-gray-200 rounded-lg focus:border-red-500 focus:outline-none transition-colors"})})]}),(0,r.jsx)("div",{className:"mb-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: Regular videos, YouTube Shorts, youtu.be links, and embed URLs"})}),(I||A)&&(0,r.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:I?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"}),(0,r.jsx)("span",{className:"text-blue-700",children:"Loading video information..."})]}):A?(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-1",children:A.title}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsxs)("p",{children:["Duration: ",Math.floor(A.duration/60),":",(A.duration%60).toString().padStart(2,"0")]}),(0,r.jsxs)("p",{children:["Uploader: ",A.uploader]}),A.uploadDate&&(0,r.jsxs)("p",{children:["Upload Date: ",new Date(A.uploadDate).toLocaleDateString()]})]})]})]}):null}),(0,r.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,r.jsxs)("button",{onClick:()=>y(!f),className:"flex items-center text-gray-600 hover:text-gray-800 transition-colors",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 mr-2"}),"Conversion Options",(0,r.jsx)(o.P.div,{animate:{rotate:180*!!f},transition:{duration:.2},className:"ml-2",children:"▼"})]})}),(0,r.jsx)(o.P.div,{initial:!1,animate:{height:f?"auto":0,opacity:+!!f},transition:{duration:.3},className:"overflow-hidden",children:(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Audio Quality"}),(0,r.jsx)("select",{value:s.bitrate,onChange:e=>m({...s,bitrate:Number(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none",children:_.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Playback Speed"}),(0,r.jsx)("select",{value:s.durationFactor,onChange:e=>m({...s,durationFactor:Number(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-red-500 focus:outline-none",children:K.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(v,{onClick:V,onMouseEnter:()=>j(!0),onMouseLeave:()=>j(!1),isLoading:x,size:"lg",className:"px-12 py-4 text-xl",disabled:!e.trim(),children:x?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"w-6 h-6 mr-2 animate-pulse"}),"Converting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"w-6 h-6 mr-2"}),"Convert to MP3"]})})})]}),(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"grid md:grid-cols-3 gap-6 text-center",children:[(0,r.jsxs)("div",{className:"p-6 bg-white rounded-xl shadow-lg",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-red-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Lightning Fast"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Convert videos in seconds with our optimized servers"})]}),(0,r.jsxs)("div",{className:"p-6 bg-white rounded-xl shadow-lg",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(n.A,{className:"w-8 h-8 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"High Quality"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Multiple bitrate options for the best audio quality"})]}),(0,r.jsxs)("div",{className:"p-6 bg-white rounded-xl shadow-lg",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(d.A,{className:"w-8 h-8 text-green-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Free Forever"}),(0,r.jsx)("p",{className:"text-gray-600",children:"No registration required, completely free to use"})]})]}),(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"grid md:grid-cols-2 gap-6 mt-8",children:[(0,r.jsxs)(p(),{href:"/history",className:"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:(0,r.jsx)(u.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold ml-4 text-gray-900",children:"View History"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track your conversion history and re-download files"})]}),(0,r.jsxs)(p(),{href:"/popular",className:"group bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold ml-4 text-gray-900",children:"Popular Today"})]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Discover today's most converted YouTube videos"})]})]}),(0,r.jsx)(F,{isVisible:C,onClose:()=>{P(!1),g(!1),k(null)},videoUrl:e,conversionId:E||void 0,options:s}),(0,r.jsx)(N,{message:R.message,type:R.type,isVisible:R.isVisible,onClose:()=>{H(e=>({...e,isVisible:!1}))}})]})}}},e=>{e.O(0,[180,343,441,255,358],()=>e(e.s=948)),_N_E=e.O()}]);