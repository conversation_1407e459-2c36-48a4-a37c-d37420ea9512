(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{829:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>b});var s=o(5155),r=o(2115),a=o(1733),n=o(1524),i=o(6132),l=o(9867);let c=(0,o(1847).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var d=o(3586),u=o(2619),m=o.n(u),p=o(4597),h=o(2549);function b(){let[e,t]=(0,r.useState)([]),[o,u]=(0,r.useState)(!0),[b,v]=(0,r.useState)(null);(0,r.useEffect)(()=>((async()=>{try{u(!0),v(null);let e=await p.v.getPopularDownloads(10);t(e),h.Z.subscribeToPopularDownloads(e=>{t(e.map(e=>({id:e.id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,thumbnailUrl:e.thumbnail_url||"",downloadCount:e.download_count,dailyCount:e.daily_count,lastUpdated:e.last_updated})))},e=>{console.error("Real-time subscription error:",e)})}catch(e){v(e instanceof Error?e.message:"Failed to load popular videos"),t([{id:"1",videoTitle:"Top Hit Song 2024 - Official Music Video",youtubeUrl:"https://youtube.com/watch?v=example1",thumbnailUrl:"https://img.youtube.com/vi/example1/maxresdefault.jpg",downloadCount:15420,dailyCount:892,lastUpdated:"2024-01-15T12:00:00Z"},{id:"2",videoTitle:"Relaxing Piano Music for Study and Work",youtubeUrl:"https://youtube.com/watch?v=example2",thumbnailUrl:"https://img.youtube.com/vi/example2/maxresdefault.jpg",downloadCount:12350,dailyCount:654,lastUpdated:"2024-01-15T11:30:00Z"},{id:"3",videoTitle:"Podcast: Tech Talk Episode 45",youtubeUrl:"https://youtube.com/watch?v=example3",thumbnailUrl:"https://img.youtube.com/vi/example3/maxresdefault.jpg",downloadCount:9876,dailyCount:432,lastUpdated:"2024-01-15T10:15:00Z"},{id:"4",videoTitle:"Nature Sounds - Rain and Thunder",youtubeUrl:"https://youtube.com/watch?v=example4",thumbnailUrl:"https://img.youtube.com/vi/example4/maxresdefault.jpg",downloadCount:8765,dailyCount:321,lastUpdated:"2024-01-15T09:45:00Z"},{id:"5",videoTitle:"Guitar Tutorial - Beginner Lesson",youtubeUrl:"https://youtube.com/watch?v=example5",thumbnailUrl:"https://img.youtube.com/vi/example5/maxresdefault.jpg",downloadCount:7654,dailyCount:298,lastUpdated:"2024-01-15T08:20:00Z"},{id:"6",videoTitle:"Meditation Music - Deep Relaxation",youtubeUrl:"https://youtube.com/watch?v=example6",thumbnailUrl:"https://img.youtube.com/vi/example6/maxresdefault.jpg",downloadCount:6543,dailyCount:267,lastUpdated:"2024-01-15T07:30:00Z"},{id:"7",videoTitle:"Comedy Sketch - Funny Moments",youtubeUrl:"https://youtube.com/watch?v=example7",thumbnailUrl:"https://img.youtube.com/vi/example7/maxresdefault.jpg",downloadCount:5432,dailyCount:234,lastUpdated:"2024-01-15T06:15:00Z"},{id:"8",videoTitle:"Workout Music - High Energy Mix",youtubeUrl:"https://youtube.com/watch?v=example8",thumbnailUrl:"https://img.youtube.com/vi/example8/maxresdefault.jpg",downloadCount:4321,dailyCount:198,lastUpdated:"2024-01-15T05:45:00Z"},{id:"9",videoTitle:"Language Learning - English Conversation",youtubeUrl:"https://youtube.com/watch?v=example9",thumbnailUrl:"https://img.youtube.com/vi/example9/maxresdefault.jpg",downloadCount:3210,dailyCount:156,lastUpdated:"2024-01-15T04:30:00Z"},{id:"10",videoTitle:"Cooking Recipe - Easy Pasta Dish",youtubeUrl:"https://youtube.com/watch?v=example10",thumbnailUrl:"https://img.youtube.com/vi/example10/maxresdefault.jpg",downloadCount:2109,dailyCount:123,lastUpdated:"2024-01-15T03:15:00Z"}])}finally{u(!1)}})(),()=>{h.Z.unsubscribeFromPopularDownloads()}),[]);let g=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString();return(0,s.jsx)("div",{className:"min-h-screen py-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(n.A,{className:"w-10 h-10 text-orange-600 mr-3"}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900",children:"Today's Top 10"})]}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:"Most popular YouTube to MP3 conversions today"})]}),(0,s.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"mb-8",children:(0,s.jsx)(m(),{href:"/",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:"← Back to Converter"})}),o?(0,s.jsx)("div",{className:"grid gap-6",children:[...Array(10)].map((e,t)=>(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-lg p-6 animate-pulse",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-300 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-300 rounded w-1/2"})]})]})},t))}):b?(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-12 text-center",children:[(0,s.jsx)(i.A,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"Error loading popular videos"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:b}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Try Again"})]}):0===e.length?(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-12 text-center",children:[(0,s.jsx)(n.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"No popular videos yet"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Start converting videos to see popular downloads here."}),(0,s.jsx)(m(),{href:"/",className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Start Converting"})]}):(0,s.jsx)("div",{className:"grid gap-6",children:e.map((e,t)=>(0,s.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*t},className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow overflow-hidden",children:(0,s.jsxs)("div",{className:"flex items-center p-6",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mr-6",children:(0,s.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg ".concat(0===t?"bg-yellow-500":1===t?"bg-gray-400":2===t?"bg-orange-600":"bg-blue-500"),children:t+1})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 truncate",children:e.videoTitle}),(0,s.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-600 mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 mr-1"}),(0,s.jsxs)("span",{children:[g(e.downloadCount)," total"]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 mr-1"}),(0,s.jsxs)("span",{children:[g(e.dailyCount)," today"]})]})]}),(0,s.jsx)("a",{href:e.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800 truncate block",children:e.youtubeUrl})]}),(0,s.jsxs)("div",{className:"flex-shrink-0 ml-6 space-x-3",children:[(0,s.jsxs)("a",{href:e.youtubeUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,s.jsx)(c,{className:"w-4 h-4 mr-2"}),"Watch"]}),(0,s.jsxs)("button",{onClick:()=>{var t;return t=e.youtubeUrl,void(window.location.href="/?url=".concat(encodeURIComponent(t)))},className:"inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Convert"]})]})]})},e.id))})]})})}},1524:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});let s=(0,o(1847).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},2549:(e,t,o)=>{"use strict";o.d(t,{Z:()=>a});var s=o(2890);class r{subscribeToConversion(e,t,o){this.unsubscribeFromConversion(e);let r=s.N.channel("conversion_progress:".concat(e)).on("postgres_changes",{event:"UPDATE",schema:"public",table:"conversions",filter:"id=eq.".concat(e)},e=>{try{let o=e.new,s={conversionId:o.id,status:o.status,progress:o.progress||0,currentStep:o.current_step||"Starting...",error:o.error_message};t(s)}catch(e){null==o||o(e)}}).on("presence",{event:"sync"},()=>{console.log("Realtime connection synced for conversion:",e)}).on("presence",{event:"join"},e=>{let{key:t,newPresences:o}=e;console.log("Joined realtime channel:",t,o)}).on("presence",{event:"leave"},e=>{let{key:t,leftPresences:o}=e;console.log("Left realtime channel:",t,o)}).subscribe(t=>{"SUBSCRIBED"===t?console.log("Successfully subscribed to conversion updates:",e):"CHANNEL_ERROR"===t&&(console.error("Error subscribing to conversion updates:",e),null==o||o(Error("Failed to subscribe to realtime updates")))});return this.subscriptions.set(e,r),r}unsubscribeFromConversion(e){let t=this.subscriptions.get(e);t&&(s.N.removeChannel(t),this.subscriptions.delete(e),console.log("Unsubscribed from conversion updates:",e))}subscribeToUserConversions(e,t,o){let r="user_conversions:".concat(e);this.unsubscribeFromUserConversions(e);let a=s.N.channel(r).on("postgres_changes",{event:"*",schema:"public",table:"conversions",filter:"user_id=eq.".concat(e)},e=>{try{if(e.new){let o=this.mapConversionFromDb(e.new);t(o)}}catch(e){null==o||o(e)}}).subscribe(t=>{"SUBSCRIBED"===t?console.log("Successfully subscribed to user conversions:",e):"CHANNEL_ERROR"===t&&(console.error("Error subscribing to user conversions:",e),null==o||o(Error("Failed to subscribe to user conversion updates")))});return this.subscriptions.set(r,a),a}unsubscribeFromUserConversions(e){let t="user_conversions:".concat(e),o=this.subscriptions.get(t);o&&(s.N.removeChannel(o),this.subscriptions.delete(t),console.log("Unsubscribed from user conversions:",e))}subscribeToPopularDownloads(e,t){let o="popular_downloads";this.unsubscribeFromPopularDownloads();let r=s.N.channel(o).on("postgres_changes",{event:"*",schema:"public",table:"popular_downloads"},async o=>{try{let{data:o,error:r}=await s.N.from("popular_downloads").select("*").order("daily_count",{ascending:!1}).limit(10);if(r){null==t||t(Error(r.message));return}e(o||[])}catch(e){null==t||t(e)}}).subscribe(e=>{"SUBSCRIBED"===e?console.log("Successfully subscribed to popular downloads"):"CHANNEL_ERROR"===e&&(console.error("Error subscribing to popular downloads"),null==t||t(Error("Failed to subscribe to popular downloads updates")))});return this.subscriptions.set(o,r),r}unsubscribeFromPopularDownloads(){let e="popular_downloads",t=this.subscriptions.get(e);t&&(s.N.removeChannel(t),this.subscriptions.delete(e),console.log("Unsubscribed from popular downloads"))}unsubscribeAll(){this.subscriptions.forEach((e,t)=>{s.N.removeChannel(e),console.log("Unsubscribed from channel:",t)}),this.subscriptions.clear()}getConnectionStatus(){return s.N.realtime.isConnected()}reconnect(){s.N.realtime.disconnect(),s.N.realtime.connect()}mapConversionFromDb(e){return{id:e.id,userId:e.user_id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,mp3Url:e.mp3_url,status:e.status,bitrate:e.bitrate,durationFactor:e.duration_factor,progress:e.progress||0,currentStep:e.current_step,error:e.error_message,createdAt:e.created_at,completedAt:e.completed_at,fileSize:e.file_size,duration:e.duration}}constructor(){this.subscriptions=new Map}}let a=new r},2890:(e,t,o)=>{"use strict";o.d(t,{N:()=>i});var s=o(5040),r=o(8969);let a=r.env.NEXT_PUBLIC_SUPABASE_URL,n=r.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;if(!a||!n)throw Error("Missing Supabase environment variables");let i=(0,s.UU)(a,n,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}})},3978:(e,t,o)=>{Promise.resolve().then(o.bind(o,829))},4597:(e,t,o)=>{"use strict";o.d(t,{v:()=>a});var s=o(2890);class r{async createConversion(e){let{data:t,error:o}=await s.N.from("conversions").insert({user_id:e.userId,youtube_url:e.youtubeUrl,video_title:e.videoTitle,bitrate:e.bitrate,duration_factor:e.durationFactor,status:e.status||"pending",progress:e.progress||0,current_step:e.currentStep}).select().single();if(o)throw Error("Failed to create conversion: ".concat(o.message));return this.mapConversionFromDb(t)}async updateConversion(e,t){let o={};t.status&&(o.status=t.status),void 0!==t.progress&&(o.progress=t.progress),t.currentStep&&(o.current_step=t.currentStep),t.mp3Url&&(o.mp3_url=t.mp3Url),t.error&&(o.error_message=t.error),t.completedAt&&(o.completed_at=t.completedAt);let{data:r,error:a}=await s.N.from("conversions").update(o).eq("id",e).select().single();if(a)throw Error("Failed to update conversion: ".concat(a.message));return this.mapConversionFromDb(r)}async getConversion(e){let{data:t,error:o}=await s.N.from("conversions").select("*").eq("id",e).single();if(o){if("PGRST116"===o.code)return null;throw Error("Failed to get conversion: ".concat(o.message))}return this.mapConversionFromDb(t)}async getUserConversions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,{data:o,error:r}=await s.N.from("conversions").select("*").eq("user_id",e).order("created_at",{ascending:!1}).limit(t);if(r)throw Error("Failed to get user conversions: ".concat(r.message));return o.map(this.mapConversionFromDb)}async getRecentConversions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,{data:t,error:o}=await s.N.from("conversions").select("*").order("created_at",{ascending:!1}).limit(e);if(o)throw Error("Failed to get recent conversions: ".concat(o.message));return t.map(this.mapConversionFromDb)}async updatePopularDownload(e,t,o){let{data:r}=await s.N.from("popular_downloads").select("*").eq("youtube_url",e).single();if(r){let{error:e}=await s.N.from("popular_downloads").update({download_count:r.download_count+1,daily_count:r.daily_count+1,last_updated:new Date().toISOString(),video_title:t,thumbnail_url:o||r.thumbnail_url}).eq("id",r.id);if(e)throw Error("Failed to update popular download: ".concat(e.message))}else{let{error:r}=await s.N.from("popular_downloads").insert({youtube_url:e,video_title:t,download_count:1,daily_count:1,thumbnail_url:o,last_updated:new Date().toISOString()});if(r)throw Error("Failed to create popular download: ".concat(r.message))}}async getPopularDownloads(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,{data:t,error:o}=await s.N.from("popular_downloads").select("*").order("daily_count",{ascending:!1}).limit(e);if(o)throw Error("Failed to get popular downloads: ".concat(o.message));return t.map(e=>({id:e.id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,thumbnailUrl:e.thumbnail_url||"",downloadCount:e.download_count,dailyCount:e.daily_count,lastUpdated:e.last_updated}))}async resetDailyCounts(){let{error:e}=await s.N.from("popular_downloads").update({daily_count:0});if(e)throw Error("Failed to reset daily counts: ".concat(e.message))}subscribeToConversion(e,t){return s.N.channel("conversion:".concat(e)).on("postgres_changes",{event:"UPDATE",schema:"public",table:"conversions",filter:"id=eq.".concat(e)},e=>{t(this.mapConversionFromDb(e.new))}).subscribe()}subscribeToUserConversions(e,t){return s.N.channel("user_conversions:".concat(e)).on("postgres_changes",{event:"*",schema:"public",table:"conversions",filter:"user_id=eq.".concat(e)},e=>{e.new&&t(this.mapConversionFromDb(e.new))}).subscribe()}mapConversionFromDb(e){return{id:e.id,userId:e.user_id,videoTitle:e.video_title,youtubeUrl:e.youtube_url,mp3Url:e.mp3_url,status:e.status,bitrate:e.bitrate,durationFactor:e.duration_factor,progress:e.progress||0,currentStep:e.current_step,error:e.error_message,createdAt:e.created_at,completedAt:e.completed_at,fileSize:e.file_size,duration:e.duration}}async cleanupOldConversions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30,t=new Date;t.setDate(t.getDate()-e);let{data:o,error:r}=await s.N.from("conversions").delete().lt("created_at",t.toISOString()).select("id");if(r)throw Error("Failed to cleanup old conversions: ".concat(r.message));return(null==o?void 0:o.length)||0}}let a=new r},6132:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});let s=(0,o(1847).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},9867:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});let s=(0,o(1847).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}},e=>{e.O(0,[180,40,441,255,358],()=>e(e.s=3978)),_N_E=e.O()}]);