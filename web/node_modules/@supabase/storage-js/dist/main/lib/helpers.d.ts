declare type Fetch = typeof fetch;
export declare const resolveFetch: (customFetch?: typeof fetch | undefined) => Fetch;
export declare const resolveResponse: () => Promise<typeof Response>;
export declare const recursiveToCamel: (item: Record<string, any>) => unknown;
/**
 * Determine if input is a plain object
 * An object is plain if it's created by either {}, new Object(), or Object.create(null)
 * source: https://github.com/sindresorhus/is-plain-obj
 */
export declare const isPlainObject: (value: object) => boolean;
export {};
//# sourceMappingURL=helpers.d.ts.map