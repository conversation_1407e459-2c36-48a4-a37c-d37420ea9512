!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.supabase=e():t.supabase=e()}(self,(()=>(()=>{"use strict";var t={907:(t,e,r)=>{var o=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw new Error("unable to locate global object")}();t.exports=e=o.fetch,o.fetch&&(e.default=o.fetch.bind(o)),e.Headers=o.Headers,e.Request=o.Request,e.Response=o.Response},478:function(t,e,r){var o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.StorageClient=void 0;const n=o(r(179)),i=o(r(19));class s extends i.default{constructor(t,e={},r,o){super(t,e,r,o)}from(t){return new n.default(this.url,this.headers,t,this.fetch)}}e.StorageClient=s},440:function(t,e,r){var o=this&&this.__createBinding||(Object.create?function(t,e,r,o){void 0===o&&(o=r);var n=Object.getOwnPropertyDescriptor(e,r);n&&!("get"in n?!e.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,o,n)}:function(t,e,r,o){void 0===o&&(o=r),t[o]=e[r]}),n=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||o(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),e.StorageClient=void 0;var i=r(478);Object.defineProperty(e,"StorageClient",{enumerable:!0,get:function(){return i.StorageClient}}),n(r(245),e),n(r(781),e)},985:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.DEFAULT_HEADERS=void 0;const o=r(560);e.DEFAULT_HEADERS={"X-Client-Info":`storage-js/${o.version}`}},781:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.StorageUnknownError=e.StorageApiError=e.isStorageError=e.StorageError=void 0;class r extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}e.StorageError=r,e.isStorageError=function(t){return"object"==typeof t&&null!==t&&"__isStorageError"in t},e.StorageApiError=class extends r{constructor(t,e,r){super(t),this.name="StorageApiError",this.status=e,this.statusCode=r}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}},e.StorageUnknownError=class extends r{constructor(t,e){super(t),this.name="StorageUnknownError",this.originalError=e}}},720:function(t,e,r){var o=this&&this.__awaiter||function(t,e,r,o){return new(r||(r=Promise))((function(n,i){function s(t){try{u(o.next(t))}catch(t){i(t)}}function a(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,a)}u((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.remove=e.head=e.put=e.post=e.get=void 0;const n=r(781),i=r(793),s=t=>t.msg||t.message||t.error_description||t.error||JSON.stringify(t);function a(t,e,r,a,u,h){return o(this,void 0,void 0,(function*(){return new Promise(((d,l)=>{t(r,((t,e,r,o)=>{const n={method:t,headers:(null==e?void 0:e.headers)||{}};return"GET"!==t&&o?((0,i.isPlainObject)(o)?(n.headers=Object.assign({"Content-Type":"application/json"},null==e?void 0:e.headers),n.body=JSON.stringify(o)):n.body=o,(null==e?void 0:e.duplex)&&(n.duplex=e.duplex),Object.assign(Object.assign({},n),r)):n})(e,a,u,h)).then((t=>{if(!t.ok)throw t;return(null==a?void 0:a.noResolveJson)?t:t.json()})).then((t=>d(t))).catch((t=>((t,e,r)=>o(void 0,void 0,void 0,(function*(){const o=yield(0,i.resolveResponse)();t instanceof o&&!(null==r?void 0:r.noResolveJson)?t.json().then((r=>{const o=t.status||500,i=(null==r?void 0:r.statusCode)||o+"";e(new n.StorageApiError(s(r),o,i))})).catch((t=>{e(new n.StorageUnknownError(s(t),t))})):e(new n.StorageUnknownError(s(t),t))})))(t,l,a)))}))}))}e.get=function(t,e,r,n){return o(this,void 0,void 0,(function*(){return a(t,"GET",e,r,n)}))},e.post=function(t,e,r,n,i){return o(this,void 0,void 0,(function*(){return a(t,"POST",e,n,i,r)}))},e.put=function(t,e,r,n,i){return o(this,void 0,void 0,(function*(){return a(t,"PUT",e,n,i,r)}))},e.head=function(t,e,r,n){return o(this,void 0,void 0,(function*(){return a(t,"HEAD",e,Object.assign(Object.assign({},r),{noResolveJson:!0}),n)}))},e.remove=function(t,e,r,n,i){return o(this,void 0,void 0,(function*(){return a(t,"DELETE",e,n,i,r)}))}},793:function(t,e,r){var o=this&&this.__createBinding||(Object.create?function(t,e,r,o){void 0===o&&(o=r);var n=Object.getOwnPropertyDescriptor(e,r);n&&!("get"in n?!e.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,o,n)}:function(t,e,r,o){void 0===o&&(o=r),t[o]=e[r]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&o(e,t,r);return n(e,t),e},s=this&&this.__awaiter||function(t,e,r,o){return new(r||(r=Promise))((function(n,i){function s(t){try{u(o.next(t))}catch(t){i(t)}}function a(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,a)}u((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.isPlainObject=e.recursiveToCamel=e.resolveResponse=e.resolveFetch=void 0,e.resolveFetch=t=>{let e;return e=t||("undefined"==typeof fetch?(...t)=>Promise.resolve().then((()=>i(r(907)))).then((({default:e})=>e(...t))):fetch),(...t)=>e(...t)},e.resolveResponse=()=>s(void 0,void 0,void 0,(function*(){return"undefined"==typeof Response?(yield Promise.resolve().then((()=>i(r(907))))).Response:Response})),e.recursiveToCamel=t=>{if(Array.isArray(t))return t.map((t=>(0,e.recursiveToCamel)(t)));if("function"==typeof t||t!==Object(t))return t;const r={};return Object.entries(t).forEach((([t,o])=>{const n=t.replace(/([-_][a-z])/gi,(t=>t.toUpperCase().replace(/[-_]/g,"")));r[n]=(0,e.recursiveToCamel)(o)})),r},e.isPlainObject=t=>{if("object"!=typeof t||null===t)return!1;const e=Object.getPrototypeOf(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||Symbol.toStringTag in t||Symbol.iterator in t)}},245:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0})},560:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.version=void 0,e.version="0.0.0"},19:function(t,e,r){var o=this&&this.__awaiter||function(t,e,r,o){return new(r||(r=Promise))((function(n,i){function s(t){try{u(o.next(t))}catch(t){i(t)}}function a(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,a)}u((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0});const n=r(985),i=r(781),s=r(720),a=r(793);e.default=class{constructor(t,e={},r,o){this.shouldThrowOnError=!1;const i=new URL(t);(null==o?void 0:o.useNewHostname)&&/supabase\.(co|in|red)$/.test(i.hostname)&&!i.hostname.includes("storage.supabase.")&&(i.hostname=i.hostname.replace("supabase.","storage.supabase.")),this.url=i.href,this.headers=Object.assign(Object.assign({},n.DEFAULT_HEADERS),e),this.fetch=(0,a.resolveFetch)(r)}throwOnError(){return this.shouldThrowOnError=!0,this}listBuckets(){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.get)(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,i.isStorageError)(t))return{data:null,error:t};throw t}}))}getBucket(t){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.get)(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,i.isStorageError)(t))return{data:null,error:t};throw t}}))}createBucket(t,e={public:!1}){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.post)(this.fetch,`${this.url}/bucket`,{id:t,name:t,type:e.type,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,i.isStorageError)(t))return{data:null,error:t};throw t}}))}updateBucket(t,e){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.put)(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,i.isStorageError)(t))return{data:null,error:t};throw t}}))}emptyBucket(t){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.post)(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,i.isStorageError)(t))return{data:null,error:t};throw t}}))}deleteBucket(t){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.remove)(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,i.isStorageError)(t))return{data:null,error:t};throw t}}))}}},179:function(t,e,r){var o=this&&this.__awaiter||function(t,e,r,o){return new(r||(r=Promise))((function(n,i){function s(t){try{u(o.next(t))}catch(t){i(t)}}function a(t){try{u(o.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,a)}u((o=o.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0});const n=r(781),i=r(720),s=r(793),a={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},u={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};e.default=class{constructor(t,e={},r,o){this.shouldThrowOnError=!1,this.url=t,this.headers=e,this.bucketId=r,this.fetch=(0,s.resolveFetch)(o)}throwOnError(){return this.shouldThrowOnError=!0,this}uploadOrUpdate(t,e,r,s){return o(this,void 0,void 0,(function*(){try{let o;const n=Object.assign(Object.assign({},u),s);let a=Object.assign(Object.assign({},this.headers),"POST"===t&&{"x-upsert":String(n.upsert)});const h=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(o=new FormData,o.append("cacheControl",n.cacheControl),h&&o.append("metadata",this.encodeMetadata(h)),o.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(o=r,o.append("cacheControl",n.cacheControl),h&&o.append("metadata",this.encodeMetadata(h))):(o=r,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,h&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(h)))),(null==s?void 0:s.headers)&&(a=Object.assign(Object.assign({},a),s.headers));const d=this._removeEmptyFolders(e),l=this._getFinalPath(d),c=yield("PUT"==t?i.put:i.post)(this.fetch,`${this.url}/object/${l}`,o,Object.assign({headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{}));return{data:{path:d,id:c.Id,fullPath:c.Key},error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}upload(t,e,r){return o(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",t,e,r)}))}uploadToSignedUrl(t,e,r,s){return o(this,void 0,void 0,(function*(){const o=this._removeEmptyFolders(t),a=this._getFinalPath(o),h=new URL(this.url+`/object/upload/sign/${a}`);h.searchParams.set("token",e);try{let t;const e=Object.assign({upsert:u.upsert},s),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(e.upsert)});return"undefined"!=typeof Blob&&r instanceof Blob?(t=new FormData,t.append("cacheControl",e.cacheControl),t.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(t=r,t.append("cacheControl",e.cacheControl)):(t=r,n["cache-control"]=`max-age=${e.cacheControl}`,n["content-type"]=e.contentType),{data:{path:o,fullPath:(yield(0,i.put)(this.fetch,h.toString(),t,{headers:n})).Key},error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}createSignedUploadUrl(t,e){return o(this,void 0,void 0,(function*(){try{let r=this._getFinalPath(t);const o=Object.assign({},this.headers);(null==e?void 0:e.upsert)&&(o["x-upsert"]="true");const s=yield(0,i.post)(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:o}),a=new URL(this.url+s.url),u=a.searchParams.get("token");if(!u)throw new n.StorageError("No token returned by API");return{data:{signedUrl:a.toString(),path:t,token:u},error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}update(t,e,r){return o(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",t,e,r)}))}move(t,e,r){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,i.post)(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:e,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}copy(t,e,r){return o(this,void 0,void 0,(function*(){try{return{data:{path:(yield(0,i.post)(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:e,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}createSignedUrl(t,e,r){return o(this,void 0,void 0,(function*(){try{let o=this._getFinalPath(t),n=yield(0,i.post)(this.fetch,`${this.url}/object/sign/${o}`,Object.assign({expiresIn:e},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${s}`)},{data:n,error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}createSignedUrls(t,e,r){return o(this,void 0,void 0,(function*(){try{const o=yield(0,i.post)(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:e,paths:t},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:o.map((t=>Object.assign(Object.assign({},t),{signedUrl:t.signedURL?encodeURI(`${this.url}${t.signedURL}${n}`):null}))),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}download(t,e){return o(this,void 0,void 0,(function*(){const r=void 0!==(null==e?void 0:e.transform)?"render/image/authenticated":"object",o=this.transformOptsToQueryString((null==e?void 0:e.transform)||{}),s=o?`?${o}`:"";try{const e=this._getFinalPath(t),o=yield(0,i.get)(this.fetch,`${this.url}/${r}/${e}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield o.blob(),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}info(t){return o(this,void 0,void 0,(function*(){const e=this._getFinalPath(t);try{const t=yield(0,i.get)(this.fetch,`${this.url}/object/info/${e}`,{headers:this.headers});return{data:(0,s.recursiveToCamel)(t),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}exists(t){return o(this,void 0,void 0,(function*(){const e=this._getFinalPath(t);try{return yield(0,i.head)(this.fetch,`${this.url}/object/${e}`,{headers:this.headers}),{data:!0,error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t)&&t instanceof n.StorageUnknownError){const e=t.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:t}}throw t}}))}getPublicUrl(t,e){const r=this._getFinalPath(t),o=[],n=(null==e?void 0:e.download)?`download=${!0===e.download?"":e.download}`:"";""!==n&&o.push(n);const i=void 0!==(null==e?void 0:e.transform)?"render/image":"object",s=this.transformOptsToQueryString((null==e?void 0:e.transform)||{});""!==s&&o.push(s);let a=o.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${r}${a}`)}}}remove(t){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,i.remove)(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}list(t,e,r){return o(this,void 0,void 0,(function*(){try{const o=Object.assign(Object.assign(Object.assign({},a),e),{prefix:t||""});return{data:yield(0,i.post)(this.fetch,`${this.url}/object/list/${this.bucketId}`,o,{headers:this.headers},r),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}listV2(t,e){return o(this,void 0,void 0,(function*(){try{const r=Object.assign({},t);return{data:yield(0,i.post)(this.fetch,`${this.url}/object/list-v2/${this.bucketId}`,r,{headers:this.headers},e),error:null}}catch(t){if(this.shouldThrowOnError)throw t;if((0,n.isStorageError)(t))return{data:null,error:t};throw t}}))}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return"undefined"!=typeof Buffer?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t.replace(/^\/+/,"")}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const e=[];return t.width&&e.push(`width=${t.width}`),t.height&&e.push(`height=${t.height}`),t.resize&&e.push(`resize=${t.resize}`),t.format&&e.push(`format=${t.format}`),t.quality&&e.push(`quality=${t.quality}`),e.join("&")}}}},e={};function r(o){var n=e[o];if(void 0!==n)return n.exports;var i=e[o]={exports:{}};return t[o].call(i.exports,i,i.exports,r),i.exports}return r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r(440)})()));