{"version": 3, "file": "SupabaseClient.d.ts", "sourceRoot": "", "sources": ["../../src/SupabaseClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAExD,OAAO,EACL,eAAe,EACf,sBAAsB,EACtB,qBAAqB,EACtB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EACL,eAAe,EACf,sBAAsB,EACtB,cAAc,EAEf,MAAM,uBAAuB,CAAA;AAC9B,OAAO,EAAE,aAAa,IAAI,qBAAqB,EAAE,MAAM,sBAAsB,CAAA;AAS7E,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAA;AAC7D,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,qBAAqB,EAA6B,MAAM,aAAa,CAAA;AAEpG;;;;GAIG;AACH,MAAM,CAAC,OAAO,OAAO,cAAc,CACjC,QAAQ,GAAG,GAAG,EAId,yBAAyB,SACrB,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,GACrD;IAAE,gBAAgB,EAAE,MAAM,CAAA;CAAE,GAAG,QAAQ,SAAS,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,GAC1F,QAAQ,GACR,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,EACvD,UAAU,SAAS,MAAM,GACvB,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,GAAG,yBAAyB,SAAS,MAAM,GACrF,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,GACxC,yBAAyB,GACzB,QAAQ,SAAS,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,GAC3D,QAAQ,GACR,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,EAAE,oBAAoB,CAAC,EACnF,MAAM,SAAS,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,UAAU,CAAC,SAAS,aAAa,GACjF,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,UAAU,CAAC,GAChD,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,UAAU,CAAC,SAAS,aAAa,GAC9E,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,UAAU,CAAC,GAChD,KAAK,EACT,aAAa,SAAS;IAAE,gBAAgB,EAAE,MAAM,CAAA;CAAE,GAAG,yBAAyB,SAAS,MAAM,GAC3F,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,GAExC,QAAQ,SAAS;IAAE,kBAAkB,EAAE;QAAE,gBAAgB,EAAE,MAAM,CAAA;KAAE,CAAA;CAAE,GACnE,QAAQ,CAAC,oBAAoB,CAAC,GAE9B;IAAE,gBAAgB,EAAE,IAAI,CAAA;CAAE,GAC5B,yBAAyB,SAAS;IAAE,gBAAgB,EAAE,MAAM,CAAA;CAAE,GAC9D,yBAAyB,GACzB,KAAK;IAsCP,SAAS,CAAC,WAAW,EAAE,MAAM;IAC7B,SAAS,CAAC,WAAW,EAAE,MAAM;IArC/B;;OAEG;IACH,IAAI,EAAE,kBAAkB,CAAA;IACxB,QAAQ,EAAE,cAAc,CAAA;IACxB;;OAEG;IACH,OAAO,EAAE,qBAAqB,CAAA;IAE9B,SAAS,CAAC,WAAW,EAAE,GAAG,CAAA;IAC1B,SAAS,CAAC,OAAO,EAAE,GAAG,CAAA;IACtB,SAAS,CAAC,UAAU,EAAE,GAAG,CAAA;IACzB,SAAS,CAAC,YAAY,EAAE,GAAG,CAAA;IAC3B,SAAS,CAAC,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC,CAAA;IACpE,SAAS,CAAC,UAAU,EAAE,MAAM,CAAA;IAC5B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAA;IACvB,SAAS,CAAC,kBAAkB,CAAC,EAAE,MAAM,CAAA;IACrC,SAAS,CAAC,WAAW,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAA;IAEpD,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAEzC;;;;;;;;;;;;OAYG;gBAES,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,EAC7B,OAAO,CAAC,EAAE,qBAAqB,CAAC,UAAU,CAAC;IAqE7C;;OAEG;IACH,IAAI,SAAS,IAAI,eAAe,CAK/B;IAGD,IAAI,CACF,SAAS,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,EACjD,KAAK,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EACzC,QAAQ,EAAE,SAAS,GAAG,qBAAqB,CAAC,aAAa,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IACtF,IAAI,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,SAAS,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAC1F,QAAQ,EAAE,QAAQ,GACjB,qBAAqB,CAAC,aAAa,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC;IAW/D;;;;;;OAMG;IACH,MAAM,CAAC,aAAa,SAAS,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,EAC9E,MAAM,EAAE,aAAa,GACpB,eAAe,CAChB,QAAQ,EACR,aAAa,EACb,aAAa,EACb,QAAQ,CAAC,aAAa,CAAC,SAAS,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,GAAG,CAC9E;IAKD;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,GAAG,CAAC,MAAM,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,EAC3F,EAAE,EAAE,MAAM,EACV,IAAI,GAAE,EAAE,CAAC,MAAM,CAAM,EACrB,OAAO,GAAE;QACP,IAAI,CAAC,EAAE,OAAO,CAAA;QACd,GAAG,CAAC,EAAE,OAAO,CAAA;QACb,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KACrC,GACL,sBAAsB,CACvB,aAAa,EACb,MAAM,EACN,EAAE,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GACvB,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACnD,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,GACrB,KAAK,GACP,KAAK,EACT,EAAE,CAAC,SAAS,CAAC,EACb,MAAM,EACN,IAAI,EACJ,KAAK,CACN;IAID;;;;;;OAMG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,GAAE,sBAAuC,GAAG,eAAe;IAIrF;;OAEG;IACH,WAAW,IAAI,eAAe,EAAE;IAIhC;;;;;OAKG;IACH,aAAa,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC;IAI9E;;OAEG;IACH,iBAAiB,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC,EAAE,CAAC;YAIhD,eAAe;IAU7B,OAAO,CAAC,uBAAuB;IAwC/B,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,oBAAoB;IAO5B,OAAO,CAAC,mBAAmB;CAgB5B"}