{"name": "clsx", "version": "2.1.1", "repository": "lukeed/clsx", "description": "A tiny (239B) utility for constructing className strings conditionally.", "module": "dist/clsx.mjs", "unpkg": "dist/clsx.min.js", "main": "dist/clsx.js", "types": "clsx.d.ts", "license": "MIT", "exports": {".": {"import": {"types": "./clsx.d.mts", "default": "./dist/clsx.mjs"}, "default": {"types": "./clsx.d.ts", "default": "./dist/clsx.js"}}, "./lite": {"import": {"types": "./clsx.d.mts", "default": "./dist/lite.mjs"}, "default": {"types": "./clsx.d.ts", "default": "./dist/lite.js"}}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=6"}, "scripts": {"build": "node bin", "test": "uvu -r esm test"}, "files": ["*.d.mts", "*.d.ts", "dist"], "keywords": ["classes", "classname", "classnames"], "devDependencies": {"esm": "3.2.25", "terser": "4.8.0", "uvu": "0.5.4"}}