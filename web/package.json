{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "lint:fix": "eslint --fix", "type-check": "tsc --noEmit", "preview": "npm run build && npm run start", "clean": "rm -rf .next"}, "dependencies": {"@supabase/supabase-js": "^2.57.4", "clsx": "^2.1.1", "framer-motion": "^12.23.16", "lucide-react": "^0.544.0", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "typescript": "^5"}}