import { NextRequest, NextResponse } from 'next/server';

const LAMBDA_API_URL = process.env.LAMBDA_API_URL || 'https://your-lambda-api.amazonaws.com/dev';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { youtubeUrl } = body;

    // Validate input
    if (!youtubeUrl) {
      return NextResponse.json(
        { error: 'YouTube URL is required' },
        { status: 400 }
      );
    }

    try {
      // Forward request to Lambda API
      const lambdaResponse = await fetch(`${LAMBDA_API_URL}/convert/info`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ youtubeUrl }),
      });

      if (!lambdaResponse.ok) {
        const errorData = await lambdaResponse.json().catch(() => ({}));
        throw new Error(errorData.message || `Lambda API error: ${lambdaResponse.status}`);
      }

      const lambdaData = await lambdaResponse.json();
      return NextResponse.json(lambdaData);
    } catch (lambdaError) {
      console.warn('Lambda API unavailable, using mock response:', lambdaError);
      
      // Extract video ID for mock response
      const videoIdMatch = youtubeUrl.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|shorts\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
      const videoId = videoIdMatch ? videoIdMatch[1] : 'unknown';
      
      // Fallback to mock response if Lambda is unavailable
      const mockVideoInfo = {
        success: true,
        data: {
          id: videoId,
          title: 'Sample Video Title (Mock Data)',
          duration: 180, // 3 minutes
          thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
          uploader: 'Sample Channel',
          uploadDate: '2024-01-01'
        }
      };

      return NextResponse.json(mockVideoInfo);
    }
  } catch (error) {
    console.error('Video info error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
