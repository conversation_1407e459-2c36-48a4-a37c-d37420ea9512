import { NextRequest, NextResponse } from 'next/server';

const LAMBDA_API_URL = process.env.LAMBDA_API_URL || 'https://your-lambda-api.amazonaws.com/dev';

export async function GET(request: NextRequest) {
  try {
    try {
      // Forward request to Lambda API
      const lambdaResponse = await fetch(`${LAMBDA_API_URL}/convert/options`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!lambdaResponse.ok) {
        const errorData = await lambdaResponse.json().catch(() => ({}));
        throw new Error(errorData.message || `Lambda API error: ${lambdaResponse.status}`);
      }

      const lambdaData = await lambdaResponse.json();
      return NextResponse.json(lambdaData);
    } catch (lambdaError) {
      console.warn('Lambda API unavailable, using default options:', lambdaError);
      
      // Fallback to default options if Lambda is unavailable
      const defaultOptions = {
        success: true,
        data: {
          bitrates: [128, 192, 320],
          durationFactors: [1.0, 1.5, 2.0],
          formats: ['mp3']
        }
      };

      return NextResponse.json(defaultOptions);
    }
  } catch (error) {
    console.error('Options error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
