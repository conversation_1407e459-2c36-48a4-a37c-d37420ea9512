import { NextRequest, NextResponse } from 'next/server';

const LAMBDA_API_URL = process.env.LAMBDA_API_URL || 'https://your-lambda-api.amazonaws.com/dev';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { youtubeUrl, bitrate, durationFactor, userId } = body;

    // Validate input
    if (!youtubeUrl) {
      return NextResponse.json(
        { error: 'YouTube URL is required' },
        { status: 400 }
      );
    }

    // Validate bitrate
    const allowedBitrates = [128, 192, 320];
    if (bitrate && !allowedBitrates.includes(bitrate)) {
      return NextResponse.json(
        { error: `Invalid bitrate. Allowed values: ${allowedBitrates.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate duration factor
    const allowedDurationFactors = [1.0, 1.5, 2.0];
    if (durationFactor && !allowedDurationFactors.includes(durationFactor)) {
      return NextResponse.json(
        { error: `Invalid duration factor. Allowed values: ${allowedDurationFactors.join(', ')}` },
        { status: 400 }
      );
    }

    try {
      // Forward request to Lambda API
      const lambdaResponse = await fetch(`${LAMBDA_API_URL}/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          youtubeUrl,
          bitrate: bitrate || 192,
          durationFactor: durationFactor || 1.0,
          userId
        }),
      });

      if (!lambdaResponse.ok) {
        const errorData = await lambdaResponse.json().catch(() => ({}));
        throw new Error(errorData.message || `Lambda API error: ${lambdaResponse.status}`);
      }

      const lambdaData = await lambdaResponse.json();
      return NextResponse.json(lambdaData);
    } catch (lambdaError) {
      console.warn('Lambda API unavailable, using mock response:', lambdaError);

      // Fallback to mock response if Lambda is unavailable
      const mockResponse = {
        success: true,
        data: {
          conversionId: `conv_${Date.now()}`,
          status: 'pending',
          message: 'Conversion started (mock mode)'
        }
      };

      return NextResponse.json(mockResponse);
    }
  } catch (error) {
    console.error('Conversion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json(
      { error: 'Conversion ID is required' },
      { status: 400 }
    );
  }

  try {
    // Forward request to Lambda API
    const lambdaResponse = await fetch(`${LAMBDA_API_URL}/convert/status?id=${encodeURIComponent(id)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!lambdaResponse.ok) {
      const errorData = await lambdaResponse.json().catch(() => ({}));
      throw new Error(errorData.message || `Lambda API error: ${lambdaResponse.status}`);
    }

    const lambdaData = await lambdaResponse.json();
    return NextResponse.json(lambdaData);
  } catch (lambdaError) {
    console.warn('Lambda API unavailable, using mock response:', lambdaError);

    // Fallback to mock response if Lambda is unavailable
    const progress = Math.min(100, Math.floor(Math.random() * 100) + 1);
    const mockStatus = {
      success: true,
      data: {
        conversionId: id,
        status: progress === 100 ? 'completed' : 'processing',
        progress,
        currentStep: progress === 100 ? 'Completed' : 'Converting audio...',
        ...(progress === 100 && { downloadUrl: 'https://example.com/download.mp3' })
      }
    };

    return NextResponse.json(mockStatus);
  }
}
