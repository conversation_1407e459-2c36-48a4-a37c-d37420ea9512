import { NextRequest, NextResponse } from 'next/server';

const LAMBDA_API_URL = process.env.LAMBDA_API_URL || 'https://your-lambda-api.amazonaws.com/dev';

export async function GET(request: NextRequest) {
  const healthStatus = {
    timestamp: new Date().toISOString(),
    services: {
      web: 'healthy',
      lambda: 'unknown',
      database: 'unknown'
    }
  };

  try {
    // Check Lambda API health
    const lambdaResponse = await fetch(`${LAMBDA_API_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });

    if (lambdaResponse.ok) {
      const lambdaHealth = await lambdaResponse.json();
      healthStatus.services.lambda = lambdaHealth.status || 'healthy';
      
      // If Lambda returns service status, merge it
      if (lambdaHealth.services) {
        healthStatus.services = { ...healthStatus.services, ...lambdaHealth.services };
      }
    } else {
      healthStatus.services.lambda = 'degraded';
    }
  } catch (error) {
    console.warn('Lambda health check failed:', error);
    healthStatus.services.lambda = 'error';
  }

  // Check database health (if enabled)
  try {
    // TODO: Add database health check when Supabase is enabled
    // const dbHealth = await supabase.from('users').select('count').limit(1);
    // healthStatus.services.database = 'healthy';
    healthStatus.services.database = 'disabled';
  } catch (error) {
    console.warn('Database health check failed:', error);
    healthStatus.services.database = 'error';
  }

  // Determine overall status
  const serviceStatuses = Object.values(healthStatus.services);
  let overallStatus = 'healthy';
  
  if (serviceStatuses.includes('error')) {
    overallStatus = 'degraded';
  } else if (serviceStatuses.includes('degraded')) {
    overallStatus = 'degraded';
  }

  const response = {
    success: true,
    data: {
      status: overallStatus,
      ...healthStatus
    }
  };

  // Return appropriate HTTP status
  const httpStatus = overallStatus === 'healthy' ? 200 : 503;
  
  return NextResponse.json(response, { status: httpStatus });
}
