import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // TODO: Fetch from database
    // For now, return mock data
    const mockPopularVideos = [
      {
        id: '1',
        videoTitle: 'Top Hit Song 2024 - Official Music Video',
        youtubeUrl: 'https://youtube.com/watch?v=example1',
        thumbnailUrl: 'https://img.youtube.com/vi/example1/maxresdefault.jpg',
        downloadCount: 15420,
        dailyCount: 892,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '2',
        videoTitle: 'Relaxing Piano Music for Study and Work',
        youtubeUrl: 'https://youtube.com/watch?v=example2',
        thumbnailUrl: 'https://img.youtube.com/vi/example2/maxresdefault.jpg',
        downloadCount: 12350,
        dailyCount: 654,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '3',
        videoTitle: 'Podcast: Tech Talk Episode 45',
        youtubeUrl: 'https://youtube.com/watch?v=example3',
        thumbnailUrl: 'https://img.youtube.com/vi/example3/maxresdefault.jpg',
        downloadCount: 9876,
        dailyCount: 432,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '4',
        videoTitle: 'Nature Sounds - Rain and Thunder',
        youtubeUrl: 'https://youtube.com/watch?v=example4',
        thumbnailUrl: 'https://img.youtube.com/vi/example4/maxresdefault.jpg',
        downloadCount: 8765,
        dailyCount: 321,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '5',
        videoTitle: 'Guitar Tutorial - Beginner Lesson',
        youtubeUrl: 'https://youtube.com/watch?v=example5',
        thumbnailUrl: 'https://img.youtube.com/vi/example5/maxresdefault.jpg',
        downloadCount: 7654,
        dailyCount: 298,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '6',
        videoTitle: 'Meditation Music - Deep Relaxation',
        youtubeUrl: 'https://youtube.com/watch?v=example6',
        thumbnailUrl: 'https://img.youtube.com/vi/example6/maxresdefault.jpg',
        downloadCount: 6543,
        dailyCount: 267,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '7',
        videoTitle: 'Comedy Sketch - Funny Moments',
        youtubeUrl: 'https://youtube.com/watch?v=example7',
        thumbnailUrl: 'https://img.youtube.com/vi/example7/maxresdefault.jpg',
        downloadCount: 5432,
        dailyCount: 234,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '8',
        videoTitle: 'Workout Music - High Energy Mix',
        youtubeUrl: 'https://youtube.com/watch?v=example8',
        thumbnailUrl: 'https://img.youtube.com/vi/example8/maxresdefault.jpg',
        downloadCount: 4321,
        dailyCount: 198,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '9',
        videoTitle: 'Language Learning - English Conversation',
        youtubeUrl: 'https://youtube.com/watch?v=example9',
        thumbnailUrl: 'https://img.youtube.com/vi/example9/maxresdefault.jpg',
        downloadCount: 3210,
        dailyCount: 156,
        lastUpdated: new Date().toISOString()
      },
      {
        id: '10',
        videoTitle: 'Cooking Recipe - Easy Pasta Dish',
        youtubeUrl: 'https://youtube.com/watch?v=example10',
        thumbnailUrl: 'https://img.youtube.com/vi/example10/maxresdefault.jpg',
        downloadCount: 2109,
        dailyCount: 123,
        lastUpdated: new Date().toISOString()
      }
    ];

    return NextResponse.json({
      success: true,
      data: mockPopularVideos,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching popular videos:', error);
    return NextResponse.json(
      { error: 'Failed to fetch popular videos' },
      { status: 500 }
    );
  }
}
