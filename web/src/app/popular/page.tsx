'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Download, Play, Music, Eye, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { databaseService } from '@/lib/database';
import { realtimeService } from '@/lib/realtime';

interface PopularVideo {
  id: string;
  videoTitle: string;
  youtubeUrl: string;
  thumbnailUrl: string;
  downloadCount: number;
  dailyCount: number;
  lastUpdated: string;
}

export default function PopularPage() {
  const [popularVideos, setPopularVideos] = useState<PopularVideo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPopularVideos = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load popular videos from database
        const videos = await databaseService.getPopularDownloads(10);
        setPopularVideos(videos);

        // Subscribe to real-time updates
        realtimeService.subscribeToPopularDownloads(
          (updatedVideos) => {
            setPopularVideos(updatedVideos.map(item => ({
              id: item.id,
              videoTitle: item.video_title,
              youtubeUrl: item.youtube_url,
              thumbnailUrl: item.thumbnail_url || '',
              downloadCount: item.download_count,
              dailyCount: item.daily_count,
              lastUpdated: item.last_updated
            })));
          },
          (error) => {
            console.error('Real-time subscription error:', error);
          }
        );
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load popular videos');
        // Fallback to mock data if database fails
        const mockData: PopularVideo[] = [
        {
          id: '1',
          videoTitle: 'Top Hit Song 2024 - Official Music Video',
          youtubeUrl: 'https://youtube.com/watch?v=example1',
          thumbnailUrl: 'https://img.youtube.com/vi/example1/maxresdefault.jpg',
          downloadCount: 15420,
          dailyCount: 892,
          lastUpdated: '2024-01-15T12:00:00Z'
        },
        {
          id: '2',
          videoTitle: 'Relaxing Piano Music for Study and Work',
          youtubeUrl: 'https://youtube.com/watch?v=example2',
          thumbnailUrl: 'https://img.youtube.com/vi/example2/maxresdefault.jpg',
          downloadCount: 12350,
          dailyCount: 654,
          lastUpdated: '2024-01-15T11:30:00Z'
        },
        {
          id: '3',
          videoTitle: 'Podcast: Tech Talk Episode 45',
          youtubeUrl: 'https://youtube.com/watch?v=example3',
          thumbnailUrl: 'https://img.youtube.com/vi/example3/maxresdefault.jpg',
          downloadCount: 9876,
          dailyCount: 432,
          lastUpdated: '2024-01-15T10:15:00Z'
        },
        {
          id: '4',
          videoTitle: 'Nature Sounds - Rain and Thunder',
          youtubeUrl: 'https://youtube.com/watch?v=example4',
          thumbnailUrl: 'https://img.youtube.com/vi/example4/maxresdefault.jpg',
          downloadCount: 8765,
          dailyCount: 321,
          lastUpdated: '2024-01-15T09:45:00Z'
        },
        {
          id: '5',
          videoTitle: 'Guitar Tutorial - Beginner Lesson',
          youtubeUrl: 'https://youtube.com/watch?v=example5',
          thumbnailUrl: 'https://img.youtube.com/vi/example5/maxresdefault.jpg',
          downloadCount: 7654,
          dailyCount: 298,
          lastUpdated: '2024-01-15T08:20:00Z'
        },
        {
          id: '6',
          videoTitle: 'Meditation Music - Deep Relaxation',
          youtubeUrl: 'https://youtube.com/watch?v=example6',
          thumbnailUrl: 'https://img.youtube.com/vi/example6/maxresdefault.jpg',
          downloadCount: 6543,
          dailyCount: 267,
          lastUpdated: '2024-01-15T07:30:00Z'
        },
        {
          id: '7',
          videoTitle: 'Comedy Sketch - Funny Moments',
          youtubeUrl: 'https://youtube.com/watch?v=example7',
          thumbnailUrl: 'https://img.youtube.com/vi/example7/maxresdefault.jpg',
          downloadCount: 5432,
          dailyCount: 234,
          lastUpdated: '2024-01-15T06:15:00Z'
        },
        {
          id: '8',
          videoTitle: 'Workout Music - High Energy Mix',
          youtubeUrl: 'https://youtube.com/watch?v=example8',
          thumbnailUrl: 'https://img.youtube.com/vi/example8/maxresdefault.jpg',
          downloadCount: 4321,
          dailyCount: 198,
          lastUpdated: '2024-01-15T05:45:00Z'
        },
        {
          id: '9',
          videoTitle: 'Language Learning - English Conversation',
          youtubeUrl: 'https://youtube.com/watch?v=example9',
          thumbnailUrl: 'https://img.youtube.com/vi/example9/maxresdefault.jpg',
          downloadCount: 3210,
          dailyCount: 156,
          lastUpdated: '2024-01-15T04:30:00Z'
        },
        {
          id: '10',
          videoTitle: 'Cooking Recipe - Easy Pasta Dish',
          youtubeUrl: 'https://youtube.com/watch?v=example10',
          thumbnailUrl: 'https://img.youtube.com/vi/example10/maxresdefault.jpg',
          downloadCount: 2109,
          dailyCount: 123,
          lastUpdated: '2024-01-15T03:15:00Z'
        }
      ];
      setPopularVideos(mockData);
      } finally {
        setIsLoading(false);
      }
    };

    loadPopularVideos();

    // Cleanup subscription on unmount
    return () => {
      realtimeService.unsubscribeFromPopularDownloads();
    };
  }, []);

  const handleConvert = (youtubeUrl: string) => {
    // Navigate to home page with pre-filled URL
    window.location.href = `/?url=${encodeURIComponent(youtubeUrl)}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-6xl mx-auto p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <TrendingUp className="w-10 h-10 text-orange-600 mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
              Today&apos;s Top 10
            </h1>
          </div>
          <p className="text-lg text-gray-600">
            Most popular YouTube to MP3 conversions today
          </p>
        </motion.div>

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <Link
            href="/"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            ← Back to Converter
          </Link>
        </motion.div>

        {/* Popular Videos List */}
        {isLoading ? (
          <div className="grid gap-6">
            {[...Array(10)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6 animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="bg-white rounded-xl shadow-lg p-12 text-center">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">Error loading popular videos</h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        ) : popularVideos.length === 0 ? (
          <div className="bg-white rounded-xl shadow-lg p-12 text-center">
            <TrendingUp className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No popular videos yet</h3>
            <p className="text-gray-500 mb-4">Start converting videos to see popular downloads here.</p>
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Start Converting
            </Link>
          </div>
        ) : (
          <div className="grid gap-6">
            {popularVideos.map((video, index) => (
              <motion.div
                key={video.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow overflow-hidden"
              >
                <div className="flex items-center p-6">
                  {/* Rank */}
                  <div className="flex-shrink-0 mr-6">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                    }`}>
                      {index + 1}
                    </div>
                  </div>

                  {/* Video Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
                      {video.videoTitle}
                    </h3>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-600 mb-3">
                      <div className="flex items-center">
                        <Download className="w-4 h-4 mr-1" />
                        <span>{formatNumber(video.downloadCount)} total</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="w-4 h-4 mr-1" />
                        <span>{formatNumber(video.dailyCount)} today</span>
                      </div>
                    </div>

                    <a
                      href={video.youtubeUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:text-blue-800 truncate block"
                    >
                      {video.youtubeUrl}
                    </a>
                  </div>

                  {/* Actions */}
                  <div className="flex-shrink-0 ml-6 space-x-3">
                    <a
                      href={video.youtubeUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Watch
                    </a>
                    <button
                      onClick={() => handleConvert(video.youtubeUrl)}
                      className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      <Music className="w-4 h-4 mr-2" />
                      Convert
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
