'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, AlertCircle, XCircle, Wifi, WifiOff } from 'lucide-react';
import { apiService } from '@/lib/api';

interface ApiStatusProps {
  showDetails?: boolean;
  className?: string;
}

interface HealthStatus {
  status: string;
  services: {
    web: string;
    lambda: string;
    database: string;
  };
  timestamp: string;
}

export default function ApiStatus({ showDetails = false, className = '' }: ApiStatusProps) {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkHealth = async () => {
    try {
      setIsLoading(true);
      const health = await apiService.healthCheck();
      setHealthStatus(health);
      setLastChecked(new Date());
    } catch (error) {
      console.error('Health check failed:', error);
      setHealthStatus({
        status: 'error',
        services: {
          web: 'healthy',
          lambda: 'error',
          database: 'unknown'
        },
        timestamp: new Date().toISOString()
      });
      setLastChecked(new Date());
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkHealth();
    
    // Check health every 30 seconds
    const interval = setInterval(checkHealth, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Wifi className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatLastChecked = () => {
    if (!lastChecked) return 'Never';
    
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - lastChecked.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else {
      return lastChecked.toLocaleTimeString();
    }
  };

  if (!showDetails && healthStatus?.status === 'healthy') {
    return null; // Don't show anything if everything is healthy and details are not requested
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={`${className}`}
      >
        <div className={`inline-flex items-center px-3 py-2 rounded-lg border ${
          healthStatus ? getStatusColor(healthStatus.status) : 'text-gray-600 bg-gray-50 border-gray-200'
        }`}>
          {isLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
          ) : (
            <div className="mr-2">
              {healthStatus ? getStatusIcon(healthStatus.status) : <WifiOff className="w-4 h-4" />}
            </div>
          )}
          
          <span className="text-sm font-medium">
            {isLoading ? 'Checking...' : healthStatus ? `API ${healthStatus.status}` : 'API Offline'}
          </span>
          
          {showDetails && (
            <span className="text-xs ml-2 opacity-75">
              ({formatLastChecked()})
            </span>
          )}
        </div>

        {showDetails && healthStatus && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-2 p-3 bg-white rounded-lg border border-gray-200 shadow-sm"
          >
            <div className="text-sm space-y-2">
              <div className="font-medium text-gray-900 mb-2">Service Status:</div>
              
              {Object.entries(healthStatus.services).map(([service, status]) => (
                <div key={service} className="flex items-center justify-between">
                  <span className="capitalize text-gray-600">{service}:</span>
                  <div className="flex items-center">
                    {getStatusIcon(status)}
                    <span className={`ml-1 text-xs font-medium ${
                      status === 'healthy' ? 'text-green-600' :
                      status === 'degraded' ? 'text-yellow-600' :
                      status === 'error' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {status}
                    </span>
                  </div>
                </div>
              ))}
              
              <div className="pt-2 mt-2 border-t border-gray-100">
                <button
                  onClick={checkHealth}
                  disabled={isLoading}
                  className="text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50"
                >
                  {isLoading ? 'Checking...' : 'Refresh Status'}
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}
