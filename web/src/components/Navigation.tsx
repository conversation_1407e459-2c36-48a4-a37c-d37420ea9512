'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Music, Clock, TrendingUp, Home, User, LogOut, LogIn } from 'lucide-react';
// import { useAuth } from '@/contexts/AuthContext';
// import AuthModal from '@/components/auth/AuthModal';
import ApiStatus from '@/components/ApiStatus';

export default function Navigation() {
  const pathname = usePathname();
  // const { user, signOut, isAuthenticated } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Temporary mock auth state for Phase 4
  const isAuthenticated = false;
  const user = null;

  const navItems = [
    {
      href: '/',
      label: 'Converter',
      icon: Home,
      active: pathname === '/'
    },
    {
      href: '/history',
      label: 'History',
      icon: Clock,
      active: pathname === '/history'
    },
    {
      href: '/popular',
      label: 'Popular',
      icon: TrendingUp,
      active: pathname === '/popular'
    }
  ];

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-6xl mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Music className="w-8 h-8 text-red-600" />
            <span className="text-xl font-bold text-gray-900">YT2MP3</span>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center space-x-8">
            <div className="flex space-x-8">
              {navItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                      item.active
                        ? 'bg-red-100 text-red-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                  </Link>
                );
              })}
            </div>

            {/* API Status */}
            <ApiStatus className="hidden md:block" />

            {/* User Menu - Temporarily disabled for Phase 4 */}
            <div className="relative">
              <button
                onClick={() => setShowAuthModal(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors opacity-50 cursor-not-allowed"
                disabled
              >
                <LogIn className="w-5 h-5" />
                <span className="font-medium">Sign In (Coming Soon)</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Auth Modal - Temporarily disabled for Phase 4 */}
      {/* <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      /> */}

      {/* Click outside to close user menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </nav>
  );
}
