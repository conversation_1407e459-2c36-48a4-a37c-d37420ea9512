import { useState, useEffect, useCallback } from 'react';
import { ConversionRecord, ConversionRequest, ConversionProgress } from '@/types';
import { conversionManager } from '@/lib/conversionManager';

export interface UseConversionOptions {
  autoCleanup?: boolean;
  pollInterval?: number;
}

export interface UseConversionReturn {
  // State
  activeConversions: ConversionRecord[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  startConversion: (request: ConversionRequest) => Promise<string>;
  cancelConversion: (conversionId: string) => Promise<boolean>;
  getConversionStatus: (conversionId: string) => Promise<ConversionRecord | null>;
  clearError: () => void;
  cleanup: () => void;
}

export function useConversion(options: UseConversionOptions = {}): UseConversionReturn {
  const { autoCleanup = true, pollInterval = 5000 } = options;
  
  const [activeConversions, setActiveConversions] = useState<ConversionRecord[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Update active conversions from conversion manager
  const updateActiveConversions = useCallback(() => {
    const conversions = conversionManager.getActiveConversions();
    setActiveConversions(conversions);
  }, []);

  // Start a new conversion
  const startConversion = useCallback(async (request: ConversionRequest): Promise<string> => {
    setIsLoading(true);
    setError(null);

    try {
      const conversionId = await conversionManager.startConversion(
        request,
        (progress: ConversionProgress) => {
          // Update the specific conversion in our state
          setActiveConversions(prev => 
            prev.map(conv => 
              conv.id === progress.conversionId 
                ? { 
                    ...conv, 
                    status: progress.status,
                    progress: progress.progress,
                    currentStep: progress.currentStep,
                    error: progress.error
                  }
                : conv
            )
          );
        },
        (result: ConversionRecord) => {
          // Conversion completed
          setActiveConversions(prev => 
            prev.map(conv => 
              conv.id === result.id ? result : conv
            )
          );
        },
        (error: Error) => {
          // Conversion failed
          setError(error.message);
          setActiveConversions(prev => 
            prev.map(conv => 
              conv.id === conversionId 
                ? { ...conv, status: 'failed', error: error.message }
                : conv
            )
          );
        }
      );

      updateActiveConversions();
      return conversionId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start conversion';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [updateActiveConversions]);

  // Cancel a conversion
  const cancelConversion = useCallback(async (conversionId: string): Promise<boolean> => {
    try {
      const success = await conversionManager.cancelConversion(conversionId);
      if (success) {
        updateActiveConversions();
      }
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to cancel conversion';
      setError(errorMessage);
      return false;
    }
  }, [updateActiveConversions]);

  // Get conversion status
  const getConversionStatus = useCallback(async (conversionId: string): Promise<ConversionRecord | null> => {
    try {
      const record = await conversionManager.getConversionStatus(conversionId);
      if (record) {
        // Update our local state
        setActiveConversions(prev => {
          const index = prev.findIndex(conv => conv.id === conversionId);
          if (index >= 0) {
            const newConversions = [...prev];
            newConversions[index] = record;
            return newConversions;
          } else {
            return [...prev, record];
          }
        });
      }
      return record;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get conversion status';
      setError(errorMessage);
      return null;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Cleanup
  const cleanup = useCallback(() => {
    conversionManager.cleanup();
    setActiveConversions([]);
    setError(null);
  }, []);

  // Auto-cleanup completed conversions
  useEffect(() => {
    if (!autoCleanup) return;

    const cleanupInterval = setInterval(() => {
      conversionManager.cleanupCompletedConversions();
      updateActiveConversions();
    }, pollInterval);

    return () => clearInterval(cleanupInterval);
  }, [autoCleanup, pollInterval, updateActiveConversions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoCleanup) {
        cleanup();
      }
    };
  }, [autoCleanup, cleanup]);

  return {
    // State
    activeConversions,
    isLoading,
    error,
    
    // Actions
    startConversion,
    cancelConversion,
    getConversionStatus,
    clearError,
    cleanup
  };
}
