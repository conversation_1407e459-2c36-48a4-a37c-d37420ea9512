import { ConversionRequest, ConversionResponse, ConversionProgress, VideoInfo } from '@/types';

export class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  }

  /**
   * Start a new conversion
   */
  async startConversion(request: ConversionRequest): Promise<ConversionResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Failed to start conversion:', error);
      throw error instanceof Error ? error : new Error('Failed to start conversion');
    }
  }

  /**
   * Get video information without starting conversion
   */
  async getVideoInfo(youtubeUrl: string): Promise<VideoInfo> {
    try {
      const response = await fetch(`${this.baseUrl}/convert/info`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ youtubeUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Failed to get video info:', error);
      throw error instanceof Error ? error : new Error('Failed to get video information');
    }
  }

  /**
   * Get conversion status
   */
  async getConversionStatus(conversionId: string): Promise<ConversionProgress> {
    try {
      const response = await fetch(`${this.baseUrl}/convert/status?id=${encodeURIComponent(conversionId)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Failed to get conversion status:', error);
      throw error instanceof Error ? error : new Error('Failed to get conversion status');
    }
  }

  /**
   * Get supported conversion options
   */
  async getSupportedOptions(): Promise<{
    bitrates: number[];
    durationFactors: number[];
    formats: string[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/convert/options`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Failed to get supported options:', error);
      // Return default options if API fails
      return {
        bitrates: [128, 192, 320],
        durationFactors: [1.0, 1.5, 2.0],
        formats: ['mp3']
      };
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: string; services: any }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'error',
        services: {
          api: 'error'
        }
      };
    }
  }

  /**
   * Cancel conversion
   */
  async cancelConversion(conversionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/convert/${conversionId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Failed to cancel conversion:', error);
      return false;
    }
  }

  /**
   * Get popular downloads from API
   */
  async getPopularDownloads(limit: number = 10): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/popular?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Failed to get popular downloads:', error);
      throw error instanceof Error ? error : new Error('Failed to get popular downloads');
    }
  }

  /**
   * Retry failed request with exponential backoff
   */
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Check if API is available
   */
  async isApiAvailable(): Promise<boolean> {
    try {
      const health = await this.healthCheck();
      return health.status === 'healthy' || health.status === 'degraded';
    } catch {
      return false;
    }
  }

  /**
   * Get API base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Set API base URL (useful for testing)
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }
}

export const apiService = new ApiService();
