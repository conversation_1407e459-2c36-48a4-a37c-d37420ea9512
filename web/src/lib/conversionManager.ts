import { apiService } from './api';
// import { databaseService } from './database';
// import { realtimeService } from './realtime';
import { ConversionRequest, ConversionRecord, ConversionProgress } from '@/types';

export interface ConversionManagerOptions {
  enableDatabase?: boolean;
  enableRealtime?: boolean;
  pollInterval?: number;
}

export class ConversionManager {
  private activeConversions: Map<string, ConversionRecord> = new Map();
  private pollIntervals: Map<string, NodeJS.Timeout> = new Map();
  private options: ConversionManagerOptions;

  constructor(options: ConversionManagerOptions = {}) {
    this.options = {
      enableDatabase: false, // Disabled for now
      enableRealtime: false, // Disabled for now
      pollInterval: 2000, // 2 seconds
      ...options
    };
  }

  /**
   * Start a new conversion
   */
  async startConversion(
    request: ConversionRequest,
    onProgress?: (progress: ConversionProgress) => void,
    onComplete?: (result: ConversionRecord) => void,
    onError?: (error: Error) => void
  ): Promise<string> {
    try {
      // Start conversion via API
      const response = await apiService.startConversion(request);
      const conversionId = response.conversionId;

      // Create initial conversion record
      const conversionRecord: ConversionRecord = {
        id: conversionId,
        userId: request.userId,
        videoTitle: 'Loading...', // Will be updated when we get video info
        youtubeUrl: request.youtubeUrl,
        status: response.status,
        bitrate: request.bitrate,
        durationFactor: request.durationFactor,
        progress: 0,
        currentStep: 'Starting conversion...',
        createdAt: new Date().toISOString()
      };

      this.activeConversions.set(conversionId, conversionRecord);

      // Save to database if enabled
      if (this.options.enableDatabase) {
        try {
          // await databaseService.createConversion(conversionRecord);
        } catch (error) {
          console.warn('Failed to save conversion to database:', error);
        }
      }

      // Set up real-time updates if enabled
      if (this.options.enableRealtime) {
        this.setupRealtimeUpdates(conversionId, onProgress, onComplete, onError);
      } else {
        // Fall back to polling
        this.setupPolling(conversionId, onProgress, onComplete, onError);
      }

      return conversionId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start conversion';
      onError?.(new Error(errorMessage));
      throw error;
    }
  }

  /**
   * Get conversion status
   */
  async getConversionStatus(conversionId: string): Promise<ConversionRecord | null> {
    // Check local cache first
    const cached = this.activeConversions.get(conversionId);
    if (cached) {
      return cached;
    }

    // Try to get from database if enabled
    if (this.options.enableDatabase) {
      try {
        // const dbRecord = await databaseService.getConversion(conversionId);
        // if (dbRecord) {
        //   this.activeConversions.set(conversionId, dbRecord);
        //   return dbRecord;
        // }
      } catch (error) {
        console.warn('Failed to get conversion from database:', error);
      }
    }

    // Fall back to API
    try {
      const progress = await apiService.getConversionStatus(conversionId);
      const record: ConversionRecord = {
        id: conversionId,
        videoTitle: 'Unknown',
        youtubeUrl: '',
        status: progress.status,
        bitrate: 192, // Default
        durationFactor: 1.0, // Default
        progress: progress.progress,
        currentStep: progress.currentStep,
        error: progress.error,
        createdAt: new Date().toISOString()
      };
      
      this.activeConversions.set(conversionId, record);
      return record;
    } catch (error) {
      console.error('Failed to get conversion status:', error);
      return null;
    }
  }

  /**
   * Cancel conversion
   */
  async cancelConversion(conversionId: string): Promise<boolean> {
    try {
      // Cancel via API
      const success = await apiService.cancelConversion(conversionId);
      
      if (success) {
        // Update local state
        const record = this.activeConversions.get(conversionId);
        if (record) {
          record.status = 'failed';
          record.error = 'Cancelled by user';
          this.activeConversions.set(conversionId, record);
        }

        // Update database if enabled
        if (this.options.enableDatabase && record) {
          try {
            // await databaseService.updateConversion(conversionId, {
            //   status: 'failed',
            //   error: 'Cancelled by user'
            // });
          } catch (error) {
            console.warn('Failed to update cancellation in database:', error);
          }
        }

        // Clean up polling
        this.stopPolling(conversionId);
      }

      return success;
    } catch (error) {
      console.error('Failed to cancel conversion:', error);
      return false;
    }
  }

  /**
   * Get all active conversions
   */
  getActiveConversions(): ConversionRecord[] {
    return Array.from(this.activeConversions.values());
  }

  /**
   * Clean up completed conversions
   */
  cleanupCompletedConversions(): void {
    for (const [id, record] of this.activeConversions.entries()) {
      if (record.status === 'completed' || record.status === 'failed') {
        this.activeConversions.delete(id);
        this.stopPolling(id);
      }
    }
  }

  /**
   * Set up real-time updates (when enabled)
   */
  private setupRealtimeUpdates(
    conversionId: string,
    onProgress?: (progress: ConversionProgress) => void,
    onComplete?: (result: ConversionRecord) => void,
    onError?: (error: Error) => void
  ): void {
    // This will be implemented when real-time is enabled
    // realtimeService.subscribeToConversion(
    //   conversionId,
    //   (progress) => {
    //     this.updateConversionProgress(conversionId, progress);
    //     onProgress?.(progress);
    //   },
    //   (error) => {
    //     onError?.(error);
    //   }
    // );
  }

  /**
   * Set up polling for conversion updates
   */
  private setupPolling(
    conversionId: string,
    onProgress?: (progress: ConversionProgress) => void,
    onComplete?: (result: ConversionRecord) => void,
    onError?: (error: Error) => void
  ): void {
    const interval = setInterval(async () => {
      try {
        const progress = await apiService.getConversionStatus(conversionId);
        
        // Update local record
        const record = this.activeConversions.get(conversionId);
        if (record) {
          record.status = progress.status;
          record.progress = progress.progress;
          record.currentStep = progress.currentStep;
          record.error = progress.error;
          
          if (progress.status === 'completed') {
            record.completedAt = new Date().toISOString();
            // record.mp3Url would be set from the API response
          }
          
          this.activeConversions.set(conversionId, record);
        }

        // Notify callbacks
        onProgress?.(progress);

        // Check if conversion is complete
        if (progress.status === 'completed' || progress.status === 'failed') {
          this.stopPolling(conversionId);
          
          if (record) {
            if (progress.status === 'completed') {
              onComplete?.(record);
            } else {
              onError?.(new Error(progress.error || 'Conversion failed'));
            }
          }
        }
      } catch (error) {
        console.error('Polling error for conversion:', conversionId, error);
        onError?.(error instanceof Error ? error : new Error('Polling failed'));
        this.stopPolling(conversionId);
      }
    }, this.options.pollInterval);

    this.pollIntervals.set(conversionId, interval);
  }

  /**
   * Stop polling for a conversion
   */
  private stopPolling(conversionId: string): void {
    const interval = this.pollIntervals.get(conversionId);
    if (interval) {
      clearInterval(interval);
      this.pollIntervals.delete(conversionId);
    }
  }

  /**
   * Update conversion progress
   */
  private updateConversionProgress(conversionId: string, progress: ConversionProgress): void {
    const record = this.activeConversions.get(conversionId);
    if (record) {
      record.status = progress.status;
      record.progress = progress.progress;
      record.currentStep = progress.currentStep;
      record.error = progress.error;
      
      if (progress.status === 'completed') {
        record.completedAt = new Date().toISOString();
      }
      
      this.activeConversions.set(conversionId, record);
    }
  }

  /**
   * Cleanup all resources
   */
  cleanup(): void {
    // Stop all polling
    for (const interval of this.pollIntervals.values()) {
      clearInterval(interval);
    }
    this.pollIntervals.clear();

    // Clear active conversions
    this.activeConversions.clear();

    // Cleanup real-time subscriptions if enabled
    if (this.options.enableRealtime) {
      // realtimeService.unsubscribeAll();
    }
  }
}

export const conversionManager = new ConversionManager();
