import { supabase } from './supabase';
import { ConversionRecord, PopularVideo } from '@/types';

export class DatabaseService {
  /**
   * Create a new conversion record
   */
  async createConversion(conversion: Omit<ConversionRecord, 'id' | 'createdAt'>): Promise<ConversionRecord> {
    const { data, error } = await supabase
      .from('conversions')
      .insert({
        user_id: conversion.userId,
        youtube_url: conversion.youtubeUrl,
        video_title: conversion.videoTitle,
        bitrate: conversion.bitrate,
        duration_factor: conversion.durationFactor,
        status: conversion.status || 'pending',
        progress: conversion.progress || 0,
        current_step: conversion.currentStep,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create conversion: ${error.message}`);
    }

    return this.mapConversionFromDb(data);
  }

  /**
   * Update conversion status and progress
   */
  async updateConversion(
    id: string, 
    updates: Partial<Pick<ConversionRecord, 'status' | 'progress' | 'currentStep' | 'mp3Url' | 'error' | 'completedAt'>>
  ): Promise<ConversionRecord> {
    const updateData: any = {};
    
    if (updates.status) updateData.status = updates.status;
    if (updates.progress !== undefined) updateData.progress = updates.progress;
    if (updates.currentStep) updateData.current_step = updates.currentStep;
    if (updates.mp3Url) updateData.mp3_url = updates.mp3Url;
    if (updates.error) updateData.error_message = updates.error;
    if (updates.completedAt) updateData.completed_at = updates.completedAt;

    const { data, error } = await supabase
      .from('conversions')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update conversion: ${error.message}`);
    }

    return this.mapConversionFromDb(data);
  }

  /**
   * Get conversion by ID
   */
  async getConversion(id: string): Promise<ConversionRecord | null> {
    const { data, error } = await supabase
      .from('conversions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get conversion: ${error.message}`);
    }

    return this.mapConversionFromDb(data);
  }

  /**
   * Get user's conversion history
   */
  async getUserConversions(userId: string, limit: number = 50): Promise<ConversionRecord[]> {
    const { data, error } = await supabase
      .from('conversions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get user conversions: ${error.message}`);
    }

    return data.map(this.mapConversionFromDb);
  }

  /**
   * Get recent conversions (for anonymous users)
   */
  async getRecentConversions(limit: number = 10): Promise<ConversionRecord[]> {
    const { data, error } = await supabase
      .from('conversions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get recent conversions: ${error.message}`);
    }

    return data.map(this.mapConversionFromDb);
  }

  /**
   * Update or create popular download entry
   */
  async updatePopularDownload(youtubeUrl: string, videoTitle: string, thumbnailUrl?: string): Promise<void> {
    // First, try to get existing entry
    const { data: existing } = await supabase
      .from('popular_downloads')
      .select('*')
      .eq('youtube_url', youtubeUrl)
      .single();

    if (existing) {
      // Update existing entry
      const { error } = await supabase
        .from('popular_downloads')
        .update({
          download_count: existing.download_count + 1,
          daily_count: existing.daily_count + 1,
          last_updated: new Date().toISOString(),
          video_title: videoTitle, // Update title in case it changed
          thumbnail_url: thumbnailUrl || existing.thumbnail_url
        })
        .eq('id', existing.id);

      if (error) {
        throw new Error(`Failed to update popular download: ${error.message}`);
      }
    } else {
      // Create new entry
      const { error } = await supabase
        .from('popular_downloads')
        .insert({
          youtube_url: youtubeUrl,
          video_title: videoTitle,
          download_count: 1,
          daily_count: 1,
          thumbnail_url: thumbnailUrl,
          last_updated: new Date().toISOString()
        });

      if (error) {
        throw new Error(`Failed to create popular download: ${error.message}`);
      }
    }
  }

  /**
   * Get popular downloads
   */
  async getPopularDownloads(limit: number = 10): Promise<PopularVideo[]> {
    const { data, error } = await supabase
      .from('popular_downloads')
      .select('*')
      .order('daily_count', { ascending: false })
      .limit(limit);

    if (error) {
      throw new Error(`Failed to get popular downloads: ${error.message}`);
    }

    return data.map(item => ({
      id: item.id,
      videoTitle: item.video_title,
      youtubeUrl: item.youtube_url,
      thumbnailUrl: item.thumbnail_url || '',
      downloadCount: item.download_count,
      dailyCount: item.daily_count,
      lastUpdated: item.last_updated
    }));
  }

  /**
   * Reset daily counts (should be called daily via cron)
   */
  async resetDailyCounts(): Promise<void> {
    const { error } = await supabase
      .from('popular_downloads')
      .update({ daily_count: 0 });

    if (error) {
      throw new Error(`Failed to reset daily counts: ${error.message}`);
    }
  }

  /**
   * Subscribe to conversion updates
   */
  subscribeToConversion(conversionId: string, callback: (conversion: ConversionRecord) => void) {
    return supabase
      .channel(`conversion:${conversionId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'conversions',
          filter: `id=eq.${conversionId}`
        },
        (payload) => {
          callback(this.mapConversionFromDb(payload.new));
        }
      )
      .subscribe();
  }

  /**
   * Subscribe to user's conversions
   */
  subscribeToUserConversions(userId: string, callback: (conversion: ConversionRecord) => void) {
    return supabase
      .channel(`user_conversions:${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversions',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          if (payload.new) {
            callback(this.mapConversionFromDb(payload.new));
          }
        }
      )
      .subscribe();
  }

  /**
   * Map database record to ConversionRecord type
   */
  private mapConversionFromDb(data: any): ConversionRecord {
    return {
      id: data.id,
      userId: data.user_id,
      videoTitle: data.video_title,
      youtubeUrl: data.youtube_url,
      mp3Url: data.mp3_url,
      status: data.status,
      bitrate: data.bitrate,
      durationFactor: data.duration_factor,
      progress: data.progress || 0,
      currentStep: data.current_step,
      error: data.error_message,
      createdAt: data.created_at,
      completedAt: data.completed_at,
      fileSize: data.file_size,
      duration: data.duration
    };
  }

  /**
   * Clean up old conversions (should be called periodically)
   */
  async cleanupOldConversions(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const { data, error } = await supabase
      .from('conversions')
      .delete()
      .lt('created_at', cutoffDate.toISOString())
      .select('id');

    if (error) {
      throw new Error(`Failed to cleanup old conversions: ${error.message}`);
    }

    return data?.length || 0;
  }
}

export const databaseService = new DatabaseService();
