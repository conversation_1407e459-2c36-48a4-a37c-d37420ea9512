import { supabase } from './supabase';
import { ConversionRecord } from '@/types';

export interface RealtimeProgress {
  conversionId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  currentStep: string;
  estimatedTimeRemaining?: number;
  error?: string;
}

export class RealtimeService {
  private subscriptions: Map<string, any> = new Map();

  /**
   * Subscribe to conversion progress updates
   */
  subscribeToConversion(
    conversionId: string,
    onUpdate: (progress: RealtimeProgress) => void,
    onError?: (error: Error) => void
  ) {
    // Unsubscribe from existing subscription if any
    this.unsubscribeFromConversion(conversionId);

    const channel = supabase
      .channel(`conversion_progress:${conversionId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'conversions',
          filter: `id=eq.${conversionId}`
        },
        (payload) => {
          try {
            const data = payload.new;
            const progress: RealtimeProgress = {
              conversionId: data.id,
              status: data.status,
              progress: data.progress || 0,
              currentStep: data.current_step || 'Starting...',
              error: data.error_message
            };
            onUpdate(progress);
          } catch (error) {
            onError?.(error as Error);
          }
        }
      )
      .on('presence', { event: 'sync' }, () => {
        console.log('Realtime connection synced for conversion:', conversionId);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('Joined realtime channel:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('Left realtime channel:', key, leftPresences);
      })
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to conversion updates:', conversionId);
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Error subscribing to conversion updates:', conversionId);
          onError?.(new Error('Failed to subscribe to realtime updates'));
        }
      });

    this.subscriptions.set(conversionId, channel);
    return channel;
  }

  /**
   * Unsubscribe from conversion updates
   */
  unsubscribeFromConversion(conversionId: string) {
    const channel = this.subscriptions.get(conversionId);
    if (channel) {
      supabase.removeChannel(channel);
      this.subscriptions.delete(conversionId);
      console.log('Unsubscribed from conversion updates:', conversionId);
    }
  }

  /**
   * Subscribe to user's conversion list updates
   */
  subscribeToUserConversions(
    userId: string,
    onUpdate: (conversion: ConversionRecord) => void,
    onError?: (error: Error) => void
  ) {
    const channelName = `user_conversions:${userId}`;
    
    // Unsubscribe from existing subscription if any
    this.unsubscribeFromUserConversions(userId);

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversions',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          try {
            if (payload.new) {
              const conversion = this.mapConversionFromDb(payload.new);
              onUpdate(conversion);
            }
          } catch (error) {
            onError?.(error as Error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to user conversions:', userId);
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Error subscribing to user conversions:', userId);
          onError?.(new Error('Failed to subscribe to user conversion updates'));
        }
      });

    this.subscriptions.set(channelName, channel);
    return channel;
  }

  /**
   * Unsubscribe from user conversions
   */
  unsubscribeFromUserConversions(userId: string) {
    const channelName = `user_conversions:${userId}`;
    const channel = this.subscriptions.get(channelName);
    if (channel) {
      supabase.removeChannel(channel);
      this.subscriptions.delete(channelName);
      console.log('Unsubscribed from user conversions:', userId);
    }
  }

  /**
   * Subscribe to popular downloads updates
   */
  subscribeToPopularDownloads(
    onUpdate: (downloads: any[]) => void,
    onError?: (error: Error) => void
  ) {
    const channelName = 'popular_downloads';
    
    // Unsubscribe from existing subscription if any
    this.unsubscribeFromPopularDownloads();

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'popular_downloads'
        },
        async (payload) => {
          try {
            // Fetch updated popular downloads list
            const { data, error } = await supabase
              .from('popular_downloads')
              .select('*')
              .order('daily_count', { ascending: false })
              .limit(10);

            if (error) {
              onError?.(new Error(error.message));
              return;
            }

            onUpdate(data || []);
          } catch (error) {
            onError?.(error as Error);
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to popular downloads');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Error subscribing to popular downloads');
          onError?.(new Error('Failed to subscribe to popular downloads updates'));
        }
      });

    this.subscriptions.set(channelName, channel);
    return channel;
  }

  /**
   * Unsubscribe from popular downloads
   */
  unsubscribeFromPopularDownloads() {
    const channelName = 'popular_downloads';
    const channel = this.subscriptions.get(channelName);
    if (channel) {
      supabase.removeChannel(channel);
      this.subscriptions.delete(channelName);
      console.log('Unsubscribed from popular downloads');
    }
  }

  /**
   * Unsubscribe from all channels
   */
  unsubscribeAll() {
    this.subscriptions.forEach((channel, key) => {
      supabase.removeChannel(channel);
      console.log('Unsubscribed from channel:', key);
    });
    this.subscriptions.clear();
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return supabase.realtime.isConnected();
  }

  /**
   * Manually reconnect
   */
  reconnect() {
    supabase.realtime.disconnect();
    supabase.realtime.connect();
  }

  /**
   * Map database record to ConversionRecord type
   */
  private mapConversionFromDb(data: any): ConversionRecord {
    return {
      id: data.id,
      userId: data.user_id,
      videoTitle: data.video_title,
      youtubeUrl: data.youtube_url,
      mp3Url: data.mp3_url,
      status: data.status,
      bitrate: data.bitrate,
      durationFactor: data.duration_factor,
      progress: data.progress || 0,
      currentStep: data.current_step,
      error: data.error_message,
      createdAt: data.created_at,
      completedAt: data.completed_at,
      fileSize: data.file_size,
      duration: data.duration
    };
  }
}

export const realtimeService = new RealtimeService();
