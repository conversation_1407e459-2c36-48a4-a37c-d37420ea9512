import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          updated_at: string;
          display_name?: string;
          avatar_url?: string;
        };
        Insert: {
          id?: string;
          email: string;
          created_at?: string;
          updated_at?: string;
          display_name?: string;
          avatar_url?: string;
        };
        Update: {
          id?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
          display_name?: string;
          avatar_url?: string;
        };
      };
      conversions: {
        Row: {
          id: string;
          user_id?: string;
          youtube_url: string;
          video_title: string;
          mp3_url?: string;
          bitrate: number;
          duration_factor: number;
          status: 'pending' | 'processing' | 'completed' | 'failed';
          progress: number;
          current_step?: string;
          error_message?: string;
          created_at: string;
          completed_at?: string;
          file_size?: number;
          duration?: number;
        };
        Insert: {
          id?: string;
          user_id?: string;
          youtube_url: string;
          video_title: string;
          mp3_url?: string;
          bitrate: number;
          duration_factor: number;
          status?: 'pending' | 'processing' | 'completed' | 'failed';
          progress?: number;
          current_step?: string;
          error_message?: string;
          created_at?: string;
          completed_at?: string;
          file_size?: number;
          duration?: number;
        };
        Update: {
          id?: string;
          user_id?: string;
          youtube_url?: string;
          video_title?: string;
          mp3_url?: string;
          bitrate?: number;
          duration_factor?: number;
          status?: 'pending' | 'processing' | 'completed' | 'failed';
          progress?: number;
          current_step?: string;
          error_message?: string;
          created_at?: string;
          completed_at?: string;
          file_size?: number;
          duration?: number;
        };
      };
      popular_downloads: {
        Row: {
          id: string;
          youtube_url: string;
          video_title: string;
          download_count: number;
          daily_count: number;
          last_updated: string;
          thumbnail_url?: string;
        };
        Insert: {
          id?: string;
          youtube_url: string;
          video_title: string;
          download_count?: number;
          daily_count?: number;
          last_updated?: string;
          thumbnail_url?: string;
        };
        Update: {
          id?: string;
          youtube_url?: string;
          video_title?: string;
          download_count?: number;
          daily_count?: number;
          last_updated?: string;
          thumbnail_url?: string;
        };
      };
    };
  };
}
