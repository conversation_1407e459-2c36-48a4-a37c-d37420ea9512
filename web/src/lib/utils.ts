import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function isValidYouTubeUrl(url: string): boolean {
  // Comprehensive YouTube URL validation including Shorts
  const youtubePatterns = [
    /^(https?:\/\/)?(www\.)?youtube\.com\/watch\?v=[\w-]+/,           // Standard watch URLs
    /^(https?:\/\/)?(www\.)?youtube\.com\/shorts\/[\w-]+/,            // YouTube Shorts
    /^(https?:\/\/)?(www\.)?youtube\.com\/embed\/[\w-]+/,             // Embed URLs
    /^(https?:\/\/)?(www\.)?youtube\.com\/v\/[\w-]+/,                 // Old format
    /^(https?:\/\/)?youtu\.be\/[\w-]+/,                               // Short URLs
    /^(https?:\/\/)?(www\.)?youtube\.com\/watch\?.*v=[\w-]+/,         // Watch with other params
  ];

  return youtubePatterns.some(pattern => pattern.test(url));
}

export function extractVideoId(url: string): string | null {
  // Comprehensive video ID extraction for all YouTube URL formats
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtube\.com\/embed\/|youtube\.com\/v\/|youtube\.com\/shorts\/|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}
