{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"app/api/convert/route.ts": {"maxDuration": 30}, "app/api/popular/route.ts": {"maxDuration": 10}}, "env": {"NODE_ENV": "production"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}